{"name": "mksaas-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "postinstall": "fumadocs-mdx", "lint": "biome check --write .", "lint:fix": "biome check --fix --unsafe .", "format": "biome format --write .", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "list-contacts": "tsx scripts/list-contacts.ts", "list-users": "tsx scripts/list-users.ts", "content": "fumadocs-mdx", "email": "email dev --dir src/mail/templates --port 3333", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "knip": "knip"}, "dependencies": {"@ai-sdk/deepseek": "^1.0.0", "@ai-sdk/fal": "^1.0.0", "@ai-sdk/fireworks": "^1.0.0", "@ai-sdk/google": "^2.0.0", "@ai-sdk/openai": "^2.0.0", "@ai-sdk/react": "^2.0.22", "@ai-sdk/replicate": "^1.0.0", "@ant-design/icons": "^6.0.1", "@anthropic-ai/sdk": "^0.60.0", "@base-ui-components/react": "1.0.0-beta.0", "@better-fetch/fetch": "^1.1.18", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fal-ai/serverless-client": "^0.15.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.2.1", "@marsidev/react-turnstile": "^1.1.0", "@mendable/firecrawl-js": "^1.29.1", "@next/third-parties": "^15.3.0", "@openpanel/nextjs": "^1.0.7", "@openrouter/ai-sdk-provider": "^1.0.0-beta.6", "@orama/orama": "^3.1.4", "@orama/tokenizers": "^3.1.4", "@playwright/test": "^1.55.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-portal": "^1.1.4", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "@react-email/components": "0.0.33", "@react-email/render": "1.0.5", "@stripe/stripe-js": "^5.6.0", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "@tanstack/react-table": "^8.21.2", "@types/canvas-confetti": "^1.9.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "ai": "^5.0.0", "antd": "^5.27.3", "better-auth": "^1.1.19", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "cookie": "^1.0.2", "crisp-sdk-web": "^1.0.25", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "dotenv": "^16.4.7", "dotted-map": "^2.2.3", "drizzle-orm": "^0.39.3", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.4.7", "fumadocs-core": "^15.6.7", "fumadocs-mdx": "^11.7.3", "fumadocs-ui": "^15.6.7", "img-comparison-slider": "^8.0.6", "input-otp": "^1.4.2", "lucide-react": "^0.483.0", "motion": "^12.4.3", "next": "15.2.1", "next-intl": "^4.0.0", "next-safe-action": "^7.10.4", "next-themes": "^0.4.4", "nuqs": "^2.5.1", "openai": "^5.16.0", "postgres": "^3.4.5", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "react-remove-scroll": "^2.6.3", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.3", "react-tweet": "^3.2.2", "react-use-measure": "^2.1.7", "recharts": "^2.15.1", "replicate": "^1.1.0", "resend": "^4.4.1", "s3mini": "^0.2.0", "shiki": "^2.4.2", "sonner": "^2.0.0", "streamdown": "^1.0.12", "stripe": "^17.6.0", "swiper": "^11.2.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "use-intl": "^3.26.5", "use-media": "^1.5.0", "use-stick-to-bottom": "^1.1.1", "vaul": "^1.1.2", "zod": "^4.0.17", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.0.14", "@tanstack/eslint-plugin-query": "^5.83.1", "@types/mdx": "^2.0.13", "@types/node": "^20.19.0", "@types/pg": "^8.11.11", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "drizzle-kit": "^0.30.4", "knip": "^5.61.2", "postcss": "^8", "react-email": "3.0.7", "tailwindcss": "^4.0.14", "tsx": "^4.19.3", "typescript": "^5.8.3"}}