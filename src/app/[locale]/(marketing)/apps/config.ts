/**
 * Shared App Configuration Types and Utilities
 *
 * This file contains the base configuration structure that all apps should inherit from.
 * It supports both static image display and interactive image comparison functionality.
 */

export interface BaseAppConfig {
  id: string;
  href: string;
  enabled: boolean;
  order?: number;
  titleKey: string;
  descriptionKey: string;
  afterImage: string; // Required: The "after" or result image
  beforeImage?: string; // Optional: The "before" or original image for comparison
}

export type AppConfig = BaseAppConfig;

/**
 * Utility function to check if an app supports image comparison
 */
export function hasImageComparison(app: AppConfig): boolean {
  return !!app.beforeImage;
}

/**
 * Utility function to get the appropriate display mode for an app
 */
export function getAppDisplayMode(app: AppConfig): 'comparison' | 'static' {
  return hasImageComparison(app) ? 'comparison' : 'static';
}
