import type { Prompt } from '@/components/ai/prompt-selector';
import type { AppConfig } from '../config';

/**
 * Figurine Application Configuration
 *
 * This file contains all figurine-specific configurations including app settings
 * and AI generation styles. Each style includes both a preview image and an
 * optimized prompt for that specific figurine type.
 */

// App configuration for the figurine application
export const appConfig: AppConfig = {
  id: 'figurine',
  afterImage: '/images/apps/figurine/figurine-after.png',
  beforeImage: '/images/apps/figurine/figurine-before.jpg',
  href: '/apps/figurine',
  enabled: true,
  order: 1,
  titleKey: 'Marketing.navbar.apps.items.figurine.title',
  descriptionKey: 'Marketing.navbar.apps.items.figurine.description',
};

export interface FigurinePrompt extends Prompt {
  // Could extend with figurine-specific properties in the future
}

export const FIGURINE_CONFIG = {
  /**
   * Available figurine prompts with preview images and optimized prompt text
   * The first prompt serves as the default option
   */
  prompts: [
    {
      id: 'modeling-scene',
      preview: '/images/apps/figurine/figurine-ai-1.png',
      prompt:
        'Create a 1/7 scale commercialized figurine of the characters in the picture, in a realistic style, in a real environment. The figurine is placed on a computer desk. The figurine has a round transparent acrylic base, with no text on the base. The content on the computer screen is a 3D modeling process of this figurine. Next to the computer screen is a toy packaging box, designed in a style reminiscent of high-quality collectible figures, printed with original artwork. The packaging features two-dimensional flat illustrations.',
    },
    {
      id: 'clean-white',
      preview: '/images/apps/figurine/figurine-ai-2.png',
      prompt:
        'Create a highly detailed 1/7 scale collectible figure of the character, standing on a clean white table. The figure is mounted on a circular transparent acrylic base. The background is minimal and plain, with soft, even lighting to emphasize realism. Focus solely on the figure and its base, isolated on the white surface. Rendered in ultra-high definition, photorealistic style.',
    },
  ] as FigurinePrompt[],
};

// Export prompts array for convenience
export const FIGURINE_PROMPTS = FIGURINE_CONFIG.prompts;
