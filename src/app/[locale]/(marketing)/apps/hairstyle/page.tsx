'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ImageResult,
  ImageUploadSection,
  PromptInput,
  ReferenceImageSelector,
} from '@/components/ai';
import type { Prompt } from '@/components/ai/prompt-selector';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useImageGeneration } from '@/hooks/use-image-generation';
import { useToast } from '@/hooks/use-toast';
import { LocaleLink } from '@/i18n/navigation';
import type { ImageGenerationRequest } from '@/lib/ai/types';
import { ArrowLeft, Palette, Sparkles } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { HAIRSTYLE_PROMPTS } from './config';

export default function HairstylePage() {
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [prompt, setPrompt] = useState(HAIRSTYLE_PROMPTS[0].prompt);
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [uploadedReferenceImage, setUploadedReferenceImage] =
    useState<File | null>(null);
  const { toast } = useToast();
  const { loading, error, result, generateImage } = useImageGeneration();
  const tNavbar = useTranslations('Marketing.navbar');
  const tCommon = useTranslations('AIApps.common');

  // Prevent global drag and drop behavior
  useEffect(() => {
    const preventGlobalDragDrop = (e: DragEvent) => {
      // Only prevent if the target is not within an upload component
      const target = e.target as HTMLElement;
      const isUploadArea = target.closest('.ant-upload') || 
                          target.closest('.image-upload-custom') || 
                          target.closest('.reference-upload-box');
      
      if (!isUploadArea) {
        e.preventDefault();
        e.stopPropagation();
      }
    };

    const preventGlobalDrop = (e: DragEvent) => {
      const target = e.target as HTMLElement;
      const isUploadArea = target.closest('.ant-upload') || 
                          target.closest('.image-upload-custom') || 
                          target.closest('.reference-upload-box');
      
      if (!isUploadArea) {
        e.preventDefault();
        e.stopPropagation();
      }
    };

    // Add event listeners to prevent global drag and drop
    document.addEventListener('dragover', preventGlobalDragDrop);
    document.addEventListener('drop', preventGlobalDrop);
    document.addEventListener('dragenter', preventGlobalDragDrop);
    document.addEventListener('dragleave', preventGlobalDragDrop);

    return () => {
      document.removeEventListener('dragover', preventGlobalDragDrop);
      document.removeEventListener('drop', preventGlobalDrop);
      document.removeEventListener('dragenter', preventGlobalDragDrop);
      document.removeEventListener('dragleave', preventGlobalDragDrop);
    };
  }, []);

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: tCommon('errors.emptyPrompt.title'),
        description: tCommon('errors.emptyPrompt.description'),
        variant: 'destructive',
      });
      return;
    }

    if (uploadedImages.length === 0) {
      toast({
        title: tCommon('errors.noImage.title'),
        description: tCommon('errors.noImage.description'),
        variant: 'destructive',
      });
      return;
    }

    if (!selectedPrompt && !uploadedReferenceImage) {
      toast({
        title: 'Reference Required',
        description:
          'Please select a hairstyle reference or upload your own reference image.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Use the first image for now, but could be extended for batch processing
      const request: ImageGenerationRequest = {
        prompt,
        image: uploadedImages[0],
        referenceImage: selectedPrompt?.preview, // Pass the selected reference image URL (if preset selected)
        referenceImageFile: uploadedReferenceImage || undefined, // Pass uploaded reference image file
      };

      await generateImage(request);
    } catch (error) {
      console.error('Generation failed:', error);
    }
  };

  const handlePromptSelect = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
    setUploadedReferenceImage(null); // Clear uploaded reference when selecting preset
    // Auto-fill prompt with prompt-specific text
    setPrompt(prompt.prompt);
  };

  const handleUploadedReferenceSelect = (file: File) => {
    setUploadedReferenceImage(file);
    setSelectedPrompt(null); // Clear preset selection when uploading
    // Keep the current prompt text when uploading reference image
  };

  return (
    <div className="hairstyle-page-wrapper">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="text-center mb-8">
          {/* Back Button - positioned at same level as badge */}
          <div className="max-w-7xl mx-auto mb-6 flex justify-start">
            <LocaleLink
              href="/apps"
              className="inline-flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="size-5" />
              {tCommon('backToApps')}
            </LocaleLink>
          </div>

          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary text-sm font-medium mb-6">
            <Sparkles className="size-5" />
            {tNavbar('apps.items.hairstyle.title')}
          </div>

          <h1 className="text-4xl font-bold text-foreground mb-4">
            {tNavbar('apps.items.hairstyle.description')}
          </h1>
        </div>

        {/* Main Generator */}
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-stretch">
            {/* Left Column - Upload and Controls */}
            <Card className="flex flex-col">
              <CardContent className="space-y-4 flex-1">
                {/* Upload Images Section */}
                <ImageUploadSection
                  onImagesChange={setUploadedImages}
                  disabled={loading}
                  maxImages={1}
                  uploadedImages={uploadedImages}
                />

                {/* Style Selection Section */}
                <div>
                  <div className="mb-2">
                    <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
                      <Palette className="size-5 text-primary" />
                      {tCommon('promptSelector.title')}
                    </h3>
                  </div>
                  <ReferenceImageSelector
                    prompts={HAIRSTYLE_PROMPTS}
                    onPromptSelect={handlePromptSelect}
                    onUploadedImageSelect={handleUploadedReferenceSelect}
                    selectedPromptId={selectedPrompt?.id}
                    uploadedReferenceImage={uploadedReferenceImage}
                    disabled={loading}
                  />
                </div>

                {/* Edit Instructions Section */}
                <div>
                  <div className="mb-2">
                    <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
                      <Sparkles className="size-5 text-primary" />
                      {tCommon('prompt.title')}
                    </h3>
                  </div>
                  <div className="space-y-4">
                    <PromptInput
                      value={prompt}
                      onChange={setPrompt}
                      disabled={loading}
                      placeholder={tCommon('prompt.placeholder')}
                      label=""
                    />

                    <GenerateButton
                      onClick={handleGenerate}
                      loading={loading}
                      disabled={
                        !prompt.trim() ||
                        uploadedImages.length === 0 ||
                        (!selectedPrompt && !uploadedReferenceImage)
                      }
                      credits={2}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Right Column - Results */}
            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-foreground">
                  {tCommon('result.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1">
                <ImageResult result={result} loading={loading} error={error} />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      
      <style jsx global>{`
        .hairstyle-page-wrapper {
          position: relative;
        }
        
        /* Ensure clicks outside upload areas don't trigger file dialogs */
        .hairstyle-page-wrapper * {
          pointer-events: auto;
        }
        
        /* Prevent any global file drop behavior */
        .hairstyle-page-wrapper {
          user-select: text;
        }
      `}</style>
    </div>
  );
}
