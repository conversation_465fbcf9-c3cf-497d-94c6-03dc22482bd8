import { ImageComparisonSlider } from '@/components/ui/image-comparison-slider';
import { LocaleLink } from '@/i18n/navigation';
import { getAvailableApps } from '@/lib/apps';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import { Sparkles } from 'lucide-react';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import type { AppConfig } from './config';
import { hasImageComparison } from './config';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata | undefined> {
  const { locale } = await params;
  const tMetadata = await getTranslations({ locale, namespace: 'Metadata' });
  const tAppsPage = await getTranslations({ locale, namespace: 'AppsPage' });

  return constructMetadata({
    title: tAppsPage('title') + ' | ' + tMetadata('title'),
    description: tAppsPage('description'),
    canonicalUrl: getUrlWithLocale('/apps', locale),
  });
}

export default async function AppsPage({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}) {
  const { locale } = await params;
  const tAppsPage = await getTranslations({ locale, namespace: 'AppsPage' });
  const tNavbar = await getTranslations({
    locale,
    namespace: 'Marketing.navbar',
  });

  const apps = await getAvailableApps();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 text-sm font-medium mb-4">
            <Sparkles className="size-4" />
            {tAppsPage('badge')}
          </div>

          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {tAppsPage('heading')}
          </h1>

          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {tAppsPage('subtitle')}
          </p>
        </div>

        {/* Apps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto pb-8">
          {/* Dynamically render all available apps */}
          {apps.map((app) => (
            <AppCard key={app.id} app={app} tNavbar={tNavbar} />
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * App Card Component
 * Renders individual app cards with configuration-driven styling and content
 * Layout: Image on top, text label on bottom (similar to the reference design)
 * Supports both static images and interactive comparison sliders
 * When comparison is available, the image area becomes interactive instead of a link
 */
function AppCard({ app, tNavbar }: { app: AppConfig; tNavbar: any }) {
  const supportsComparison = hasImageComparison(app);

  const CardContent = (
    <div className="group relative overflow-hidden rounded-xl bg-white shadow-lg hover:shadow-xl transition-shadow duration-300">
      {/* Main Image Area */}
      <div className="relative aspect-[4/3] w-full bg-gray-200">
        {supportsComparison && app.beforeImage ? (
          // Use comparison slider when both before and after images are available
          <ImageComparisonSlider
            beforeImage={app.beforeImage}
            afterImage={app.afterImage}
            className="w-full h-full"
            hover={true}
          />
        ) : (
          // Use static image when only after image is available
          <img
            src={app.afterImage}
            alt={tNavbar(app.titleKey.replace('Marketing.navbar.', ''))}
            className="w-full h-full object-cover"
          />
        )}

        {/* Hover overlay - only show for non-comparison images */}
        {!supportsComparison && (
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
        )}
      </div>

      {/* Bottom Text Label - always clickable */}
      <LocaleLink href={app.href}>
        <div className="relative bg-white border-t border-gray-200/50 cursor-pointer hover:bg-gray-50 transition-colors duration-200">
          <div className="px-4 py-3">
            <h3 className="text-lg font-semibold text-gray-900 mb-1 truncate">
              {tNavbar(app.titleKey.replace('Marketing.navbar.', ''))}
            </h3>
            <p className="text-sm text-gray-600 line-clamp-2 min-h-[2.5rem]">
              {tNavbar(app.descriptionKey.replace('Marketing.navbar.', ''))}
            </p>
          </div>
        </div>
      </LocaleLink>
    </div>
  );

  // If comparison is supported, don't wrap the whole card with link
  if (supportsComparison) {
    return CardContent;
  }

  // If no comparison, wrap the whole card with link (original behavior)
  return (
    <LocaleLink href={app.href} className="cursor-pointer">
      {CardContent}
    </LocaleLink>
  );
}
