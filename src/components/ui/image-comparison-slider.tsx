'use client';

import { useEffect, useRef } from 'react';

interface ImageComparisonSliderProps {
  beforeImage: string;
  afterImage: string;
  className?: string;
  hover?: boolean;
}

export function ImageComparisonSlider({
  beforeImage,
  afterImage,
  className = '',
  hover = true,
}: ImageComparisonSliderProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Dynamically import and register the custom element
    import('img-comparison-slider').then(() => {
      if (containerRef.current) {
        const slider = document.createElement('img-comparison-slider');
        if (hover) {
          slider.setAttribute('hover', 'hover');
        }
        slider.className = 'w-full h-full';

        const afterImg = document.createElement('img');
        afterImg.setAttribute('slot', 'first');
        afterImg.src = afterImage;
        afterImg.alt = 'After';
        afterImg.className = 'w-full h-full object-cover';

        const beforeImg = document.createElement('img');
        beforeImg.setAttribute('slot', 'second');
        beforeImg.src = beforeImage;
        beforeImg.alt = 'Before';
        beforeImg.className = 'w-full h-full object-cover';

        slider.appendChild(afterImg);
        slider.appendChild(beforeImg);

        containerRef.current.appendChild(slider);
      }
    });

    // Cleanup function
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    };
  }, [beforeImage, afterImage, hover]);

  return (
    <div 
      ref={containerRef} 
      className={`${className} img-comparison-no-handle`}
      style={{
        '--divider-width': '0px',
        '--divider-color': 'transparent',
        '--default-handle-opacity': '0',
      } as React.CSSProperties}
    />
  );
}