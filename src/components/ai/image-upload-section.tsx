'use client';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Info, Upload } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { ImageUploadGrid } from './image-upload-grid';

interface ImageUploadSectionProps {
  onImagesChange: (images: File[]) => void;
  disabled?: boolean;
  maxImages?: number;
  uploadedImages: File[];
}

export function ImageUploadSection({
  onImagesChange,
  disabled = false,
  maxImages = 9,
  uploadedImages,
}: ImageUploadSectionProps) {
  const [infoPopoverOpen, setInfoPopoverOpen] = useState(false);
  const tCommon = useTranslations('AIApps.common');

  return (
    <div>
      <div className="mb-2">
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Upload className="size-5 text-primary" />
          {tCommon('uploadImage.title')}
          <Popover open={infoPopoverOpen} onOpenChange={setInfoPopoverOpen}>
            <PopoverTrigger asChild>
              <Info className="size-5 text-muted-foreground hover:text-foreground cursor-pointer transition-colors" />
            </PopoverTrigger>
            <PopoverContent
              avoidCollisions={true}
              collisionPadding={10}
              className="max-w-xs bg-background border border-border text-foreground shadow-md"
            >
              <div className="space-y-1">
                <div className="text-sm font-medium text-foreground">
                  {uploadedImages.length} / {maxImages}{' '}
                  {tCommon('uploadImage.maxImages')}
                </div>
                <div className="text-xs text-muted-foreground">
                  {tCommon('uploadImage.supportedFormats')}
                </div>
                <div className="text-xs text-muted-foreground">
                  {tCommon('uploadImage.maxFileSize')}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </h3>
      </div>
      <ImageUploadGrid
        onImagesChange={onImagesChange}
        disabled={disabled}
        maxImages={maxImages}
        layout="grid"
      />
    </div>
  );
}
