'use client';

import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { uploadFileFromBrowser } from '@/storage/client';
import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';

interface UploadedImage {
  file: File;
  url: string;
  uploadUrl?: string;
}

interface ImageUploadGridProps {
  onImagesChange: (files: File[]) => void;
  disabled?: boolean;
  maxImages?: number;
  layout?: 'grid' | 'single';
  title?: string;
  description?: string;
  className?: string;
}

export function ImageUploadGrid({
  onImagesChange,
  disabled = false,
  maxImages = 1,
  layout = 'single',
  title = 'Select Image',
  description = 'Supports JPG, PNG, WebP formats',
  className,
}: ImageUploadGridProps) {
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const { toast } = useToast();
  const t = useTranslations('AIApps.common');
  const containerRef = useRef<HTMLDivElement>(null);

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      uploadedImages.forEach((image) => {
        URL.revokeObjectURL(image.url);
      });
    };
  }, []);

  // Update parent component when images change
  useEffect(() => {
    onImagesChange(uploadedImages.map((img) => img.file));
  }, [uploadedImages, onImagesChange]);

  const validateFile = useCallback(
    (file: File): boolean => {
      // Check file size (max 10MB)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        toast({
          title: 'File too large',
          description: 'File size must be less than 10MB',
          variant: 'destructive',
        });
        return false;
      }

      // Check file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        toast({
          title: 'Invalid file type',
          description: 'Please upload a valid image file (JPG, PNG, or WebP)',
          variant: 'destructive',
        });
        return false;
      }

      return true;
    },
    [toast]
  );

  const processFiles = async (files: File[]) => {
    if (files.length === 0) return;

    // Check if adding these files would exceed max limit
    if (uploadedImages.length + files.length > maxImages) {
      toast({
        title: t('errors.maxImagesReached.title'),
        description: t('errors.maxImagesReached.description'),
        variant: 'destructive',
      });
      return;
    }

    // Validate all files first
    const validFiles = files.filter(validateFile);
    if (validFiles.length === 0) {
      return;
    }

    setIsUploading(true);

    try {
      const newImages: UploadedImage[] = [];

      for (const file of validFiles) {
        // Create preview URL
        const previewUrl = URL.createObjectURL(file);

        try {
          // Upload to storage
          const { url: uploadUrl } = await uploadFileFromBrowser(
            file,
            'ai-images'
          );

          newImages.push({
            file,
            url: previewUrl,
            uploadUrl,
          });
        } catch (error) {
          console.error('Upload failed for file:', file.name, error);
          URL.revokeObjectURL(previewUrl);
          toast({
            title: 'Upload failed',
            description: `Failed to upload ${file.name}`,
            variant: 'destructive',
          });
        }
      }

      if (newImages.length > 0) {
        setUploadedImages((prev) => [...prev, ...newImages]);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    await processFiles(files);
    e.target.value = ''; // Reset input
  };

  // Handle drag and drop events
  const handleDragEnter = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (!disabled && !isUploading) {
        setDragActive(true);
      }
    },
    [disabled, isUploading]
  );

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (disabled || isUploading) return;

      const files = Array.from(e.dataTransfer.files).filter((file) =>
        file.type.startsWith('image/')
      );
      processFiles(files);
    },
    [disabled, isUploading, processFiles]
  );

  // Handle clipboard paste
  const handlePaste = useCallback(
    (e: ClipboardEvent) => {
      if (disabled || isUploading) return;

      const items = Array.from(e.clipboardData?.items || []);
      const imageItems = items.filter((item) => item.type.startsWith('image/'));

      if (imageItems.length > 0) {
        e.preventDefault();
        const files = imageItems
          .map((item) => item.getAsFile())
          .filter((file) => file !== null) as File[];
        processFiles(files);
      }
    },
    [disabled, isUploading, processFiles]
  );

  // Set up paste event listener
  useEffect(() => {
    const handleGlobalPaste = (e: ClipboardEvent) => {
      // Only handle paste if the container is focused or visible
      if (containerRef.current && document.contains(containerRef.current)) {
        handlePaste(e);
      }
    };

    document.addEventListener('paste', handleGlobalPaste);
    return () => {
      document.removeEventListener('paste', handleGlobalPaste);
    };
  }, [handlePaste]);

  const removeImage = useCallback((index: number) => {
    setUploadedImages((prev) => {
      const newImages = [...prev];
      const removedImage = newImages[index];

      // Clean up object URL
      URL.revokeObjectURL(removedImage.url);

      newImages.splice(index, 1);
      return newImages;
    });
  }, []);

  // Render grid layout
  const renderGridLayout = () => (
    <div
      ref={containerRef}
      className={`flex flex-wrap gap-4 relative ${dragActive ? 'ring-2 ring-primary ring-offset-2 bg-primary/5' : ''}`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* Display uploaded images */}
      {uploadedImages.map((image, index) => (
        <div
          key={index}
          className="border-2 border-border rounded-lg overflow-hidden bg-muted/20 aspect-square"
          style={{ width: 'calc(33.333% - 0.75rem)' }}
        >
          <div className="relative w-full h-full">
            <img
              src={image.url}
              alt={`Upload #${index + 1}`}
              className="w-full h-full object-cover"
            />
            <Button
              size="sm"
              variant="destructive"
              className="absolute top-1 right-1 h-5 w-5 p-0 rounded-full"
              onClick={() => removeImage(index)}
              disabled={disabled || isUploading}
            >
              <X className="h-2 w-2" />
            </Button>
          </div>
        </div>
      ))}

      {/* Add button if under limit */}
      {uploadedImages.length < maxImages && (
        <div
          className={`border-2 border-dashed rounded-lg overflow-hidden bg-muted/20 hover:border-muted-foreground/50 transition-colors aspect-square ${
            dragActive ? 'border-primary bg-primary/10' : 'border-border'
          }`}
          style={{ width: 'calc(33.333% - 0.75rem)' }}
        >
          <label className="flex flex-col items-center justify-center w-full h-full cursor-pointer hover:bg-muted/40 transition-colors">
            <input
              type="file"
              accept="image/*"
              multiple
              onChange={handleFileChange}
              disabled={disabled || isUploading}
              className="hidden"
              id="image-upload-grid"
            />
            <div className="text-center">
              <div className="text-xl text-muted-foreground/60 mb-1">
                {isUploading ? '⏳' : '+'}
              </div>
              <p className="text-xs text-foreground">
                {dragActive
                  ? 'Drop images here'
                  : isUploading
                    ? t('uploadImage.uploading')
                    : t('uploadImage.addImage')}
              </p>
              {!dragActive && !isUploading && (
                <p className="text-xs text-muted-foreground mt-1">
                  {t('uploadImage.dragDropPaste')}
                </p>
              )}
            </div>
          </label>
        </div>
      )}

      {/* Drag overlay for when dragging over the entire container */}
      {dragActive && (
        <div className="absolute inset-0 bg-primary/10 border-2 border-dashed border-primary rounded-lg flex items-center justify-center z-10 pointer-events-none">
          <div className="text-center">
            <div className="text-3xl text-primary mb-2">📁</div>
            <p className="text-lg font-medium text-primary">Drop images here</p>
            <p className="text-sm text-muted-foreground">Release to upload</p>
          </div>
        </div>
      )}
    </div>
  );

  // Render single layout
  const renderSingleLayout = () => (
    <div
      ref={containerRef}
      className={`border border-dashed rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors bg-muted/20 relative ${
        dragActive
          ? 'border-primary bg-primary/10 ring-2 ring-primary ring-offset-2'
          : 'border-border'
      }`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        disabled={disabled || isUploading}
        className="hidden"
        id="image-upload-single"
      />
      <label htmlFor="image-upload-single" className="cursor-pointer block">
        {uploadedImages.length > 0 ? (
          <div className="space-y-4">
            <img
              src={uploadedImages[0].url}
              alt="Uploaded preview"
              className="max-h-48 mx-auto rounded-lg shadow-md"
            />
            <div className="space-y-2">
              <p className="text-sm font-medium text-foreground">
                Selected: {uploadedImages[0].file.name}
              </p>
              <div className="flex items-center justify-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="border border-border text-foreground hover:bg-muted/40 cursor-pointer"
                  disabled={disabled || isUploading}
                  onClick={(e) => {
                    e.preventDefault();
                    document.getElementById('image-upload-single')?.click();
                  }}
                >
                  Change Image
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={(e) => {
                    e.preventDefault();
                    removeImage(0);
                  }}
                  disabled={disabled || isUploading}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-muted-foreground/60 text-6xl">📷</div>
            <div className="space-y-2">
              <p className="text-lg font-medium text-foreground">
                {dragActive ? 'Drop image here' : title}
              </p>
              <p className="text-sm text-muted-foreground">
                {dragActive ? 'Release to upload' : description}
              </p>
              {!dragActive && (
                <p className="text-xs text-muted-foreground">
                  {t('uploadImage.dragDropPaste')}
                </p>
              )}
            </div>
            <Button
              type="button"
              variant="outline"
              className="border border-border text-foreground hover:bg-muted/40 cursor-pointer"
              disabled={disabled || isUploading}
              onClick={(e) => {
                e.preventDefault();
                document.getElementById('image-upload-single')?.click();
              }}
            >
              {isUploading ? (
                <>
                  <div className="text-xl mr-2">⏳</div>
                  {t('uploadImage.uploading')}
                </>
              ) : (
                <>
                  <div className="text-xl mr-2">📷</div>
                  Select Image
                </>
              )}
            </Button>
          </div>
        )}
      </label>
    </div>
  );

  return (
    <div className={className}>
      {layout === 'single' ? renderSingleLayout() : renderGridLayout()}
    </div>
  );
}
