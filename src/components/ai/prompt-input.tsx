'use client';

import { Textarea } from '@/components/ui/textarea';

interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  label?: string;
  className?: string;
}

export function PromptInput({
  value,
  onChange,
  disabled,
  placeholder = 'Describe the image you want to generate...',
  label = 'Prompt',
  className,
}: PromptInputProps) {
  return (
    <div className="space-y-2">
      <label htmlFor="prompt-input" className="text-sm font-medium">
        {label}
      </label>
      <Textarea
        id="prompt-input"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        placeholder={placeholder}
        className={`h-48 resize-none overflow-y-auto ${className}`}
      />
    </div>
  );
}
