'use client';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

export interface Prompt {
  id: string;
  preview: string;
  prompt: string;
}

interface PromptSelectorProps {
  prompts: Prompt[];
  onPromptSelect: (prompt: Prompt) => void;
  selectedPromptId?: string;
  disabled?: boolean;
  className?: string;
}

export function PromptSelector({
  prompts,
  onPromptSelect,
  selectedPromptId,
  disabled = false,
  className,
}: PromptSelectorProps) {
  const [selectedPrompt, setSelectedPrompt] = useState<string | undefined>(
    selectedPromptId
  );
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  const handlePromptSelect = (prompt: Prompt) => {
    if (disabled) return;

    setSelectedPrompt(prompt.id);
    onPromptSelect(prompt);
  };

  const handleImageError = (promptId: string) => {
    setImageErrors((prev) => new Set(prev.add(promptId)));
  };

  return (
    <div className={className}>
      {/* Scrollable Grid Container - Show exactly 2 rows */}
      <div
        style={{ maxHeight: '280px' }}
        className="overflow-y-auto overscroll-contain"
      >
        <div className="flex flex-wrap gap-4">
          {prompts.map((prompt) => (
            <div
              key={prompt.id}
              className={cn(
                'border-2 border-border rounded-lg overflow-hidden bg-muted/20 aspect-square cursor-pointer transition-all duration-300 hover:border-primary/50 hover:shadow-lg',
                selectedPrompt === prompt.id &&
                  'border-primary shadow-xl hover:border-primary',
                disabled && 'opacity-50 cursor-not-allowed hover:border-border'
              )}
              style={{ width: 'calc(33.333% - 0.75rem)' }}
              onClick={() => handlePromptSelect(prompt)}
            >
              <div className="relative w-full h-full">
                {prompt.preview && !imageErrors.has(prompt.id) ? (
                  <Image
                    src={prompt.preview}
                    alt={`${prompt.id} preview`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    onError={() => handleImageError(prompt.id)}
                  />
                ) : (
                  /* Fallback placeholder */
                  <div className="absolute inset-0 bg-gradient-to-br from-muted-foreground/20 to-muted-foreground/40 flex items-center justify-center text-muted-foreground">
                    <div className="text-center px-2">
                      <div className="text-sm font-medium">{prompt.id}</div>
                      <div className="text-xs opacity-70 mt-1">Preview</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
