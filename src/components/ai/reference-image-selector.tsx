'use client';

import type { Prompt } from '@/components/ai/prompt-selector';
import { PromptSelector } from '@/components/ai/prompt-selector';

export interface ReferenceImageSelectorProps {
  prompts: Prompt[];
  onPromptSelect: (prompt: Prompt) => void;
  // Optional props for future/custom reference image upload support
  onUploadedImageSelect?: (file: File | null) => void;
  uploadedReferenceImage?: File | null;
  selectedPromptId?: string;
  disabled?: boolean;
  className?: string;
}

// Minimal implementation that wraps the existing PromptSelector.
// It accepts extra props for compatibility but currently only renders the preset selector.
export function ReferenceImageSelector({
  prompts,
  onPromptSelect,
  selectedPromptId,
  disabled = false,
  className,
}: ReferenceImageSelectorProps) {
  return (
    <PromptSelector
      prompts={prompts}
      onPromptSelect={onPromptSelect}
      selectedPromptId={selectedPromptId}
      disabled={disabled}
      className={className}
    />
  );
}

