'use client';

import { Upload } from 'antd';
import type { UploadFile, UploadProps } from 'antd';
import { useTranslations } from 'next-intl';
import { useState, useCallback, useMemo, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

interface AntdImageUploadProps {
  onImagesChange: (images: File[]) => void;
  disabled?: boolean;
  maxImages?: number;
  uploadedImages?: File[];
  layout?: 'grid' | 'single';
  className?: string;
  showTitle?: boolean;
  title?: string;
}

export function AntdImageUpload({
  onImagesChange,
  disabled = false,
  maxImages = 1,
  uploadedImages = [],
  layout = 'single',
  className = '',
  showTitle = true,
  title,
}: AntdImageUploadProps) {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { toast } = useToast();
  const tCommon = useTranslations('AIApps.common');

  // Sync fileList when uploadedImages prop changes
  useEffect(() => {
    const newFileList: UploadFile[] = uploadedImages.map((file, index) => ({
      uid: `${file.name}-${index}`,
      name: file.name,
      status: 'done',
      originFileObj: file,
      url: URL.createObjectURL(file),
      preview: URL.createObjectURL(file), // Add preview URL for Ant Design's built-in preview
    }));
    setFileList(newFileList);
  }, [uploadedImages]);

  // Derive uploaded File[] from fileList
  const derivedFiles = useMemo(() =>
    fileList
      .map((f) => f.originFileObj as File | undefined)
      .filter(Boolean) as File[],
    [fileList]
  );

  // Handle file list changes
  const handleChange: UploadProps['onChange'] = ({ fileList: fl }) => {
    setFileList(fl);
    
    // Extract File objects and notify parent
    const files = fl
      .map((f) => f.originFileObj as File | undefined)
      .filter(Boolean) as File[];
    
    onImagesChange(files);
  };



  // Validate file before upload
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload an image file (JPG, PNG, WebP)',
        variant: 'destructive',
      });
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      toast({
        title: 'File too large',
        description: 'Image must be smaller than 10MB',
        variant: 'destructive',
      });
      return false;
    }

    return false; // Prevent auto upload
  };

  const displayTitle = title || tCommon('uploadImage.title');

  // Render grid layout (multiple images)
  const renderGridLayout = () => (
    <div className={`space-y-4 ${className}`}>
      {showTitle && (
        <div className="mb-2">
          <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
            {displayTitle}
          </h3>
        </div>
      )}
      
      <div className="flex flex-wrap gap-4">
        {Array.from({ length: Math.min(maxImages, 3) }).map((_, index) => {
          const hasFile = index < fileList.length;
          return (
            <div
              key={index}
              className="image-upload-custom border-2 rounded-lg overflow-hidden bg-muted/20 border-border aspect-square"
              style={{ width: 'calc(33.333% - 0.75rem)' }}
            >
              <Upload
                accept="image/*"
                listType="picture-card"
                maxCount={1}
                multiple={false}
                disabled={disabled}
                fileList={hasFile ? [fileList[index]] : []}
                beforeUpload={beforeUpload}
                onChange={({ fileList: fl }) => {
                  const newFileList = [...fileList];
                  if (fl.length > 0) {
                    newFileList[index] = fl[0];
                  } else {
                    newFileList.splice(index, 1);
                  }
                  handleChange({ fileList: newFileList });
                }}

                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true,
                  showDownloadIcon: false,
                }}
              >
                {!hasFile && (
                  <div className="w-full h-full flex flex-col items-center justify-center text-muted-foreground">
                    <div className="text-2xl">＋</div>
                    <div className="text-xs">{tCommon('uploadImage.addImage')}</div>
                  </div>
                )}
              </Upload>
            </div>
          );
        })}
      </div>
    </div>
  );

  // Render single layout (one image)
  const renderSingleLayout = () => (
    <div className={`space-y-4 ${className}`}>
      {showTitle && (
        <div className="mb-2">
          <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
            {displayTitle}
          </h3>
        </div>
      )}

      <div className="flex flex-wrap gap-4">
        <div
          className="image-upload-custom border-2 rounded-lg overflow-hidden bg-muted/20 border-border aspect-square"
          style={{ width: 'calc(33.333% - 0.75rem)' }}
        >
          <Upload
            accept="image/*"
            listType="picture-card"
            maxCount={1}
            multiple={false}
            disabled={disabled}
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={handleChange}

            showUploadList={{
              showPreviewIcon: true,
              showRemoveIcon: true,
              showDownloadIcon: false,
            }}
          >
            {fileList.length >= 1 ? null : (
              <div className="w-full h-full flex flex-col items-center justify-center text-muted-foreground">
                <div className="text-2xl">＋</div>
                <div className="text-xs">{tCommon('uploadImage.addImage')}</div>
              </div>
            )}
          </Upload>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {layout === 'grid' ? renderGridLayout() : renderSingleLayout()}

      {/* Styles for Ant Design Upload component */}
      <style jsx global>{`
        /* Make AntD picture-card fill our square container */
        .image-upload-custom {
          position: relative;
          height: 100%;
          width: 100%;
        }
        .image-upload-custom .ant-upload-wrapper,
        .image-upload-custom .ant-upload-picture-card-wrapper {
          height: 100% !important;
          width: 100% !important;
          display: block !important; /* avoid flex shrink of the button */
        }
        .image-upload-custom .ant-upload,
        .image-upload-custom .ant-upload-select,
        .image-upload-custom .ant-upload-list-item-container,
        .image-upload-custom .ant-upload-list-item,
        .image-upload-custom .ant-upload-select-picture-card {
          width: 100% !important;
          height: 100% !important;
          min-height: 100% !important;
        }
        /* Remove default margins and center content and force full overlay */
        .image-upload-custom .ant-upload-select-picture-card {
          position: absolute;
          inset: 0;
          margin: 0 !important;
          border: 2px dashed var(--border);
          background: hsl(var(--muted) / 0.2);
          display: flex !important;
          align-items: center;
          justify-content: center;
        }
        .image-upload-custom .ant-upload-select-picture-card > .ant-upload {
          display: flex !important;
          width: 100% !important;
          height: 100% !important;
          align-items: center;
          justify-content: center;
          border: none !important;
          background: transparent !important;
        }
        .image-upload-custom .ant-upload-list {
          margin: 0 !important;
          height: 100% !important;
          width: 100% !important;
        }
        .image-upload-custom .ant-upload-list-item {
          margin: 0 !important;
          padding: 0 !important;
        }
        .image-upload-custom .ant-upload-list-item-image {
          width: 100% !important;
          height: 100% !important;
          object-fit: cover !important;
        }
      `}</style>
    </>
  );
}
