import type { AppConfig } from '@/app/[locale]/(marketing)/apps/config';
import { appConfig as figurineConfig } from '@/app/[locale]/(marketing)/apps/figurine/config';
import { appConfig as hairstyleConfig } from '@/app/[locale]/(marketing)/apps/hairstyle/config';

// Static registry of all available apps
// This is more reliable than dynamic file system scanning in production environments
const ALL_APPS: AppConfig[] = [
  figurineConfig,
  hairstyleConfig,
  // Add more apps here as they are created
];

/**
 * Get all available apps using static registry
 * This approach works reliably in both development and production environments
 */
export async function getAvailableApps(): Promise<AppConfig[]> {
  // Filter only enabled apps and sort by order
  return ALL_APPS.filter((app) => app.enabled).sort((a, b) => {
    const orderA = a.order ?? 999;
    const orderB = b.order ?? 999;
    return orderA - orderB;
  });
}

/**
 * Get a specific app config by ID
 */
export async function getAppConfig(appId: string): Promise<AppConfig | null> {
  const apps = await getAvailableApps();
  return apps.find((app) => app.id === appId) || null;
}
