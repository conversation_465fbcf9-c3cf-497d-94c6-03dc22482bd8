{"version": "0.2.0", "compounds": [{"name": "Debug Full Stack", "configurations": ["Next.js: debug client-side", "Next.js: debug server-side"], "stopAll": true, "presentation": {"hidden": false, "group": "", "order": 1}}], "configurations": [{"name": "Next.js: debug client-side", "type": "node", "request": "launch", "runtimeExecutable": "pnpm", "runtimeArgs": ["run", "dev"], "cwd": "${workspaceFolder}", "skipFiles": ["<node_internals>/**"], "env": {"NODE_OPTIONS": "--inspect=9229"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "serverReadyAction": {"pattern": "- Local:\\s+([^\\s]+)", "uriFormat": "%s", "action": "openExternally"}}, {"name": "Next.js: debug server-side", "type": "node", "request": "attach", "port": 9230, "skipFiles": ["<node_internals>/**"], "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}", "restart": true, "timeout": 30000}]}