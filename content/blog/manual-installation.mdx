---
title: Manual Installation
description: Create a new fumadocs project from scratch.
image: /images/blog/post-4.png
date: "2025-03-14"
published: true
categories: [company, product]
author: mkdirs
---

> Read the [Quick Start](/docs) guide first for basic concept.

## Getting Started

Create a new Next.js application with `create-next-app`, and install required packages.

```mdx
fumadocs-ui fumadocs-core
```

### Content Source

Fumadocs supports different content sources, you can choose one you prefer.

There is a list of officially supported sources:

- [Setup Fumadocs MDX](/docs/mdx)
- [Setup Content Collections](/docs/headless/content-collections)

Make sure to configure the library correctly following their setup guide before continuing, we will import the source adapter using `@/lib/source.ts` in this guide.

### Root Layout

Wrap the entire application inside [Root Provider](/docs/layouts/root-provider), and add required styles to `body`.

```tsx
import { RootProvider } from 'fumadocs-ui/provider';
import type { ReactNode } from 'react';

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        // you can use Tailwind CSS too
        style={{
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
        }}
      >
        <RootProvider>{children}</RootProvider>
      </body>
    </html>
  );
}
```

### Styles

Setup Tailwind CSS v4 on your Next.js app, add the following to `global.css`.

```css title="Tailwind CSS"
@import 'tailwindcss';
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';

/* path of `fumadocs-ui` relative to the CSS file */
@source '../node_modules/fumadocs-ui/dist/**/*.js';
```

> It doesn't come with a default font, you may choose one from `next/font`.

### Layout

Create a `app/layout.config.tsx` file to put the shared options for our layouts.

```json doc-gen:file
{
  "file": "../../examples/next-mdx/app/layout.config.tsx",
  "codeblock": {
    "meta": "title=\"app/layout.config.tsx\""
  }
}
```

Create a folder `/app/docs` for our docs, and give it a proper layout.

```json doc-gen:file
{
  "file": "../../examples/next-mdx/app/docs/layout.tsx",
  "codeblock": {
    "meta": "title=\"app/docs/layout.tsx\""
  }
}
```

> `pageTree` refers to Page Tree, it should be provided by your content source.

### Page

Create a catch-all route `/app/docs/[[...slug]]` for docs pages.

In the page, wrap your content in the [Page](/docs/layouts/page) component.
It may vary depending on your content source. You should configure static rendering with `generateStaticParams` and metadata with `generateMetadata`.

<Tabs items={['Fumadocs MDX', 'Content Collections']}>

<Tab value='Fumadocs MDX'>
```json doc-gen:file
{
  "file": "../../examples/next-mdx/app/docs/[[...slug]]/page.tsx",
  "codeblock": {
    "meta": "title=\"app/docs/[[...slug]]/page.tsx\" tab=\"Fumadocs MDX\""
  }
}
```
</Tab>

<Tab value='Content Collections'>
```json doc-gen:file
{
  "file": "../../examples/content-collections/app/docs/[[...slug]]/page.tsx",
  "codeblock": {
    "meta": "title=\"app/docs/[[...slug]]/page.tsx\" tab=\"Content Collections\""
  }
}
```
</Tab>

</Tabs>

### Search

Use the default document search based on Orama.

<Tabs items={['Fumadocs MDX', 'Content Collections']}>

<Tab value='Fumadocs MDX'>
```json doc-gen:file
{
  "file": "../../examples/next-mdx/app/api/search/route.ts",
  "codeblock": {
    "meta": "title=\"app/api/search/route.ts\" tab=\"Fumadocs MDX\""
  }
}
```
</Tab>

<Tab value='Content Collections'>
```json doc-gen:file
{
  "file": "../../examples/content-collections/app/api/search/route.ts",
  "codeblock": {
    "meta": "title=\"app/api/search/route.ts\" tab=\"Content Collections\""
  }
}
```
</Tab>

</Tabs>

Learn more about [Document Search](/docs/headless/search).

### Done

You can start the dev server and create MDX files.

```mdx title="content/docs/index.mdx"
---
title: Hello World
---

## Introduction

I love Anime.
```

## Customise

You can use [Home Layout](/docs/layouts/home-layout) for other pages of the site, it includes a navbar with theme toggle.

## Deploying

It should work out-of-the-box with Vercel & Netlify.

### Docker Deployment

If you want to deploy your Fumadocs app using Docker with **Fumadocs MDX configured**, make sure to add the `source.config.ts` file to the `WORKDIR` in the Dockerfile.
The following snippet is taken from the official [Next.js Dockerfile Example](https://github.com/vercel/next.js/blob/canary/examples/with-docker/Dockerfile):

```zsh title="Dockerfile"
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* .npmrc* source.config.ts ./
```

This ensures Fumadocs MDX can access your configuration file during builds.
