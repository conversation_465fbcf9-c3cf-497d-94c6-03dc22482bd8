---
title: 主题
description: 为 Fumadocs UI 添加主题
image: /images/blog/post-7.png
date: "2025-01-15"
published: true
categories: [product, news]
author: mkdirs
---

## 使用方法

注意只支持 Tailwind CSS v4：

```css title="Tailwind CSS"
@import 'tailwindcss';
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';

/* path of `fumadocs-ui` relative to the CSS file */
@source '../node_modules/fumadocs-ui/dist/**/*.js';
```

### 预设更改

通过使用 Tailwind CSS 插件或预构建的样式表，您的默认边框、文本和背景颜色将被更改。

### 明/暗模式

Fumadocs 通过 [`next-themes`](https://github.com/pacocoursey/next-themes) 支持明/暗模式，它包含在 Root Provider 中。

参见 [Root Provider](/docs/layouts/root-provider#theme-provider) 了解更多信息。

### RTL 布局

支持 RTL（从右到左）布局。

要启用 RTL，请在 body 和 root provider（Radix UI 需要）中将 `dir` 属性设置为 `rtl`。

```tsx
import { RootProvider } from 'fumadocs-ui/provider';
import type { ReactNode } from 'react';

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body dir="rtl">
        <RootProvider dir="rtl">{children}</RootProvider>
      </body>
    </html>
  );
}
```

### 前缀

Fumadocs UI 有自己的颜色、动画和工具。
默认情况下，它添加了 `fd-` 前缀，以避免与 Shadcn UI 或您自己的 CSS 变量冲突。

您可以通过添加一些别名来使用它们，而无需前缀：

```css title="Tailwind CSS"
@theme {
  --color-primary: var(--color-fd-primary);
}
```

> 您可以将其与 CSS 媒体查询一起使用，实现响应式设计。

### 布局宽度

使用 CSS 变量自定义文档布局的最大宽度。

```css
:root {
  --fd-layout-width: 1400px;
}
```

{/* <WidthTrigger /> */}

## Tailwind CSS 预设

Tailwind CSS 预设引入了新的颜色和额外的工具，包括 `fd-steps`。

### 主题

它开箱即用地提供了许多主题，您可以选择一个您喜欢的。

```css
@import 'fumadocs-ui/css/<theme>.css';

/* Example */
@import 'fumadocs-ui/css/black.css';
```

<Tabs items={['neutral', 'black', 'vitepress', 'dusk', 'catppuccin', 'ocean', 'purple']}>

    <Tab value='neutral'>

![Neutral](/images/docs/themes/neutral.png)

    </Tab>

    <Tab value='black'>

![Black](/images/docs/themes/black.png)

    </Tab>

    <Tab value='vitepress'>

![Vitepress](/images/docs/themes/vitepress.png)

    </Tab>

    <Tab value='dusk'>

![Dusk](/images/docs/themes/dusk.png)

    </Tab>

    <Tab value='Catppuccin'>

![Catppuccin](/images/docs/themes/catppuccin.png)

    </Tab>

    <Tab value='ocean'>

![Ocean](/images/docs/themes/ocean.png)

    </Tab>

    <Tab value='purple'>

![Purple](/images/docs/themes/purple.png)

    </Tab>

</Tabs>

### 颜色

设计系统的灵感来自 [Shadcn UI](https://ui.shadcn.com)，您可以使用 CSS 变量轻松自定义颜色。

```css title="global.css"
:root {
  --color-fd-background: hsl(0, 0%, 100%);
}

.dark {
  --color-fd-background: hsl(0, 0%, 0%);
}
```

### 排版

我们有一个内置插件，它是从 [Tailwind CSS Typography](https://tailwindcss.com/docs/typography-plugin) 派生而来的。

该插件添加了一个 `prose` 类和变体来自定义它。

```tsx
<div className="prose">
  <h1>Good Heading</h1>
</div>
```

> 该插件仅与 Fumadocs UI 的 MDX 组件一起工作，它可能与 `@tailwindcss/typography` 冲突。
> 如果您需要使用 `@tailwindcss/typography` 而不是默认插件，请[设置类名选项](https://github.com/tailwindlabs/tailwindcss-typography/blob/main/README.md#changing-the-default-class-name)以避免冲突。