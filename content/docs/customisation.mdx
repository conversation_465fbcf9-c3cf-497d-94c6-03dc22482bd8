---
title: Overview
description: An overview of Fumadocs UI
---

## Architecture

### Page Tree

Navigation elements like sidebar take a [Page Tree](/docs/headless/page-tree) to render navigation links, it's a tree that describes all available pages and folders.

Normally, it is generated from your file structure using [`loader()`](/docs/headless/source-api), you can learn [how to organize pages](/docs/page-conventions).

## Customisation

### Layouts

You can use the exposed options of different layouts:

<Cards>
  <Card title="Docs Layout" href="/docs/layouts/docs">
    Layout for docs
  </Card>
  <Card title="Docs Page" href="/docs/layouts/page">
    Layout for docs content
  </Card>
  <Card title="Notebook Layout" href="/docs/layouts/notebook">
    A more compact version of Docs Layout
  </Card>
  <Card title="Home Layout" href="/docs/layouts/home-layout">
    Layout for other pages
  </Card>
</Cards>

### Components

Fumadocs UI also offers styled components for interactive examples to enhance your docs, you can customise them with exposed props like `style` and `className`.

See [Components](/docs/components).

### Design System

Since the design system is built on Tailwind CSS, you can customise it [with CSS Variables](/docs/theme#colors).

### CLI

If none of them suits you, Fumadocs CLI is a tool to install Fumadocs UI components and layouts to your codebase, similar to Shadcn UI. Allowing you to fully customise Fumadocs UI:

```mdx
npx fumadocs add
```
