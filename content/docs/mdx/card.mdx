---
title: Card
description: Use the Card component in your MDX documentation
preview: card
---

## Usage

Add it to your MDX components.

```tsx
import { Card, Cards } from 'fumadocs-ui/components/card';

<MDX
  components={{
    Card,
    Cards,
  }}
/>;
```

See [Markdown](/docs/markdown#cards) for usages.

### Cards

The container of cards.

### Card

Based on Next.js `<Link />`.

{/* <AutoTypeTable path="./content/docs/props.ts" name="CardProps" /> */}

<Callout title="Tree Shaking on icons" type="warn">

If you're not using Fumadocs MDX for rendering MDX (e.g. using Contentlayer), ensure that
tree shaking is working properly.

Most of the icon libraries support importing icons individually.

```tsx
import HomeIcon from 'lucide-react/dist/esm/icons/home';
```

As a workaround, you can pass icons to MDX Components too. (this uses Next.js bundler instead of content source)

```tsx title="page.tsx"
import { HomeIcon } from 'lucide-react';

const components = {
  ...defaultComponents,
  HomeIcon,
};
```

</Callout>
