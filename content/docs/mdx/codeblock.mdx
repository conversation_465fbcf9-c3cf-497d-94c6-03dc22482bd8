---
title: Code Block
description: Adding code blocks to your docs
---

<Wrapper>
<div className="bg-fd-background rounded-lg prose-no-margin">

```js title="config.js"
import createMDX from 'fumadocs-mdx/config';

const withMDX = createMDX();

// [!code word:config]
/** @type {import('next').NextConfig} */
const config = {
  // [!code highlight]
  reactStrictMode: true, // [!code highlight]
}; // [!code highlight]

export default withMDX(config);
```

</div>
</Wrapper>

Display code blocks, added by default.

- Copy button
- Custom titles and icons

## Usage

Wrap the pre element in `<CodeBlock />`, which acts as the wrapper of code block.

```tsx
import { Pre, CodeBlock } from 'fumadocs-ui/components/codeblock';

<MDX
  components={{
    // HTML `ref` attribute conflicts with `forwardRef`
    pre: ({ ref: _ref, ...props }) => (
      <CodeBlock {...props}>
        <Pre>{props.children}</Pre> {/* [!code highlight] */}
      </CodeBlock>
    ),
  }}
/>;
```

See [Markdown](/docs/markdown#codeblock) for usages.

### Keep Background

Use the background color generated by Shiki (the Rehype Code plugin).

```tsx
import { Pre, CodeBlock } from 'fumadocs-ui/components/codeblock';

<MDX
  components={{
    pre: ({ ref: _ref, ...props }) => (
      <CodeBlock keepBackground {...props}>
        <Pre>{props.children}</Pre>
      </CodeBlock>
    ),
  }}
/>;
```

### Icons

Specify a custom icon by passing an `icon` prop to `CodeBlock` component.

By default, the icon will be injected by the custom Shiki transformer.

```js title="config.js"
console.log('js');
```
