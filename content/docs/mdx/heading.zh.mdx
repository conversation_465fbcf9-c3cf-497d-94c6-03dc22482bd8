---
title: 标题
description: MDX 文档的标题组件
preview: heading
---

自动添加 `id` 属性的标题组件。

## 使用方法

将其添加到您的 MDX 组件中，从 `h1` 到 `h6`。

```mdx
import { Heading } from 'fumadocs-ui/components/heading';

<MDX
  components={{
    h1: (props) => <Heading as="h1" {...props} />,
    h2: (props) => <Heading as="h2" {...props} />,
    h3: (props) => <Heading as="h3" {...props} />,
    h4: (props) => <Heading as="h4" {...props} />,
    h5: (props) => <Heading as="h5" {...props} />,
    h6: (props) => <Heading as="h6" {...props} />,
  }}
/>
``` 