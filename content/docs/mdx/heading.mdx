---
title: Heading
description: Heading components for your MDX documentation
preview: heading
---

The heading component which automatically adds the `id` prop.

## Usage

Add it to your MDX components, from `h1` to `h6`.

```mdx
import { Heading } from 'fumadocs-ui/components/heading';

<MDX
  components={{
    h1: (props) => <Heading as="h1" {...props} />,
    h2: (props) => <Heading as="h2" {...props} />,
    h3: (props) => <Heading as="h3" {...props} />,
    h4: (props) => <Heading as="h4" {...props} />,
    h5: (props) => <Heading as="h5" {...props} />,
    h6: (props) => <Heading as="h6" {...props} />,
  }}
/>
```
