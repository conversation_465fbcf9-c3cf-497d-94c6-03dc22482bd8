---
title: Markdown
description: How to write documents
---

## Introduction

Fumadocs provides many useful extensions to MDX, a markup language. Here is a brief introduction to the default MDX syntax of Fumadocs UI.

> MDX is not the only supported format of Fumadocs. In fact, you can use any renderers such as `next-mdx-remote` or CMS.

## Markdown

We use GFM (GitHub Flavored Markdown), a superset of Markdown (CommonMark).
See [GFM Specification](https://github.github.com/gfm).

````md
# Heading

## Heading

### Heading

#### Heading

Hello World, **Bold**, _Italic_, ~~Hidden~~

```js
console.log('Hello World');
```

1. First
2. Second
3. Third

- Item 1
- Item 2

> Quote here

![alt](/image.png)

| Table | Description |
| ----- | ----------- |
| Hello | World       |
````

### Auto Links

Internal links use the `next/link` component to allow prefetching and avoid hard-reload.

External links will get the default `rel="noreferrer noopener" target="_blank"` attributes for security.

```mdx
[My Link](https://github.github.com/gfm)

This also works: https://github.github.com/gfm.
```

## MDX

MDX is a superset of Markdown, with support of JSX syntax.
It allows you to import components, and use them right in the document, or even export values.

```mdx
import { Component } from './component';

<Component name="Hello" />
```

see [MDX Syntax](https://mdxjs.com/docs/what-is-mdx/#mdx-syntax) to learn more.

### Cards

Useful for adding links, it is included by default.

```mdx
<Cards>
  <Card
    href="https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating"
    title="Fetching, Caching, and Revalidating"
  >
    Learn more about caching in Next.js
  </Card>
</Cards>
```

<Cards>
  <Card
    href="https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating"
    title="Fetching, Caching, and Revalidating"
  >
    Learn more about caching in Next.js
  </Card>
</Cards>

#### Icon

You can specify an icon to cards.

```mdx
import { HomeIcon } from 'lucide-react';

<Cards>
  <Card icon={<HomeIcon />} href="/" title="Home">
    Go back to home
  </Card>
</Cards>
```

<Cards>
  <Card icon={<HomeIcon />} href="/" title="Go back to home">
    The home page of Fumadocs.
  </Card>
</Cards>

#### Without href

```mdx
<Cards>
  <Card title="Fetching, Caching, and Revalidating">
    Learn more about `fetch` in Next.js.
  </Card>
</Cards>
```

<Cards>
  <Card title="Fetching, Caching, and Revalidating">
    Learn more about `fetch` in Next.js.
  </Card>
</Cards>

### Callouts

Useful for adding tips/warnings, it is included by default.

```mdx
<Callout>Hello World</Callout>
```

<Callout>Hello World</Callout>

#### Title

Specify a callout title.

```mdx
<Callout title="Title">Hello World</Callout>
```

<Callout title="Title">Hello World</Callout>

#### Types

You can specify the type of callout.

- `info` (default)
- `warn`
- `error`

```mdx
<Callout title="Title" type="error">
  Hello World
</Callout>
```

<Callout title="Title" type="error">
  Hello World
</Callout>

### Customise Components

See [all MDX components and available options](/docs/mdx).

## Headings

An anchor is automatically applied to each heading, it sanitizes invalid characters like spaces. (e.g. `Hello World` to `hello-world`)

```md
# Hello `World`
```

### TOC Settings

The table of contents (TOC) will be generated based on headings, you can also customise the effects of headings:

```md
# Heading [!toc]

This heading will be hidden from TOC.

# Another Heading [toc]

This heading will **only** be visible in TOC, you can use it to add additional TOC items.
Like headings rendered in a React component:

<MyComp />
```

### Custom Anchor

You can add `[#slug]` to customise heading anchors.

```md
# heading [#my-heading-id]
```

You can also chain it with TOC settings like:

```md
# heading [toc] [#my-heading-id]
```

To link people to a specific heading, add the heading id to hash fragment: `/page#my-heading-id`.

## Frontmatter

We support YAML frontmatter. It is a way to specify common information of the document (e.g. title).
Place it at the top of document.

```mdx
---
title: Hello World
---

## Title
```

See [Page Conventions](/docs/page-conventions#frontmatter) for a list of properties available for frontmatter.

## Codeblock

Syntax Highlighting is supported by default using [Rehype Code](/docs/headless/mdx/rehype-code).

````mdx
```js
console.log('Hello World');
```
````

You can add a title to the codeblock.

````mdx
```js title="My Title"
console.log('Hello World');
```
````

### Highlight Lines

You can highlight specific lines by adding `[!code highlight]`.

````md
```tsx
<div>Hello World</div>  // [\!code highlight]
<div>Hello World</div>
<div>Goodbye</div>
<div>Hello World</div>
```
````

### Highlight Words

You can highlight a specific word by adding `[!code word:<match>]`.

````md
```js
// [\!code word:config]
const config = {
  reactStrictMode: true,
};
```
````

### Diffs

````mdx
```ts
console.log('hewwo'); // [\!code --]
console.log('hello'); // [\!code ++]
```
````

```ts
console.log('hewwo'); // [!code --]
console.log('hello'); // [!code ++]
```

### Tab Groups

You can use code blocks with the `<Tab />` component.

````mdx
import { Tab, Tabs } from 'fumadocs-ui/components/tabs';

<Tabs items={['Tab 1', 'Tab 2']}>
  <Tab value='Tab 1'>
    ```ts
    console.log('A');
    ```
  </Tab>
  <Tab value='Tab 2'>
    ```ts
    console.log('B');
    ```
  </Tab>
</Tabs>
````

> Note that you can add MDX components instead of importing them in MDX files.

<Tabs items={['Tab 1', 'Tab 2']}>
  <Tab value='Tab 1'>
    ```ts
    console.log('A');
    ```
  </Tab>
  <Tab value='Tab 2'>
    ```ts
    console.log('B');
    ```
  </Tab>
</Tabs>

### Using Typescript Twoslash

Write Typescript codeblocks with hover type information and detected types errors.

Not enabled by default. See [Twoslash](/docs/twoslash).

## Images

All built-in content sources handle images properly.
Images are automatically optimized for `next/image`.

```mdx
![Image](/image.png)
```

![Image](/images/docs/notebook.png)

## Optional

Some optional plugins you can enable.

### Math Equations

Write math equations with TeX.

````md
```mdx
f(x) = x * e^{2 pi i \xi x}
```
````

```mdx
f(x) = x * e^{2 pi i \xi x}
```

To enable, see [Math Integration](/docs/math).

### Package Install

Generate code blocks for installing packages via package managers (JS/Node.js).

````md
```mdx
npm i next -D
```
````

```mdx
npm i next -D
```

To enable, see [Remark Install](/docs/headless/mdx/install).

### More

You can see [a list of plugins](/docs/headless/mdx) supported by Fumadocs.
