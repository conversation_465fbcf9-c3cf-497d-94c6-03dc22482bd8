---
title: Markdown
description: 如何撰写文档
---

## 介绍

Fumadocs 为 MDX（一种标记语言）提供了许多有用的扩展。以下是 Fumadocs UI 默认 MDX 语法的简要介绍。

> MDX 不是 Fumadocs 唯一支持的格式。实际上，您可以使用任何渲染器，如 `next-mdx-remote` 或 CMS。

## Markdown

我们使用 GFM（GitHub 风格的 Markdown），这是 Markdown（CommonMark）的超集。
参见 [GFM 规范](https://github.github.com/gfm)。

````md
# Heading

## Heading

### Heading

#### Heading

Hello World, **Bold**, _Italic_, ~~Hidden~~

```js
console.log('Hello World');
```

1. First
2. Second
3. Third

- Item 1
- Item 2

> Quote here

![alt](/image.png)

| Table | Description |
| ----- | ----------- |
| Hello | World       |
````

### 自动链接

内部链接使用 `next/link` 组件，允许预取并避免硬重载。

外部链接将获得默认的 `rel="noreferrer noopener" target="_blank"` 属性以增强安全性。

```mdx
[My Link](https://github.github.com/gfm)

This also works: https://github.github.com/gfm.
```

## MDX

MDX 是 Markdown 的超集，支持 JSX 语法。
它允许您导入组件，并直接在文档中使用它们，甚至导出值。

```mdx
import { Component } from './component';

<Component name="Hello" />
```

参见 [MDX 语法](https://mdxjs.com/docs/what-is-mdx/#mdx-syntax) 了解更多信息。

### 卡片

对于添加链接很有用，默认包含。

```mdx
<Cards>
  <Card
    href="https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating"
    title="Fetching, Caching, and Revalidating"
  >
    Learn more about caching in Next.js
  </Card>
</Cards>
```

<Cards>
  <Card
    href="https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating"
    title="Fetching, Caching, and Revalidating"
  >
    Learn more about caching in Next.js
  </Card>
</Cards>

#### 图标

您可以为卡片指定图标。

```mdx
import { HomeIcon } from 'lucide-react';

<Cards>
  <Card icon={<HomeIcon />} href="/" title="Home">
    Go back to home
  </Card>
</Cards>
```

<Cards>
  <Card icon={<HomeIcon />} href="/" title="Go back to home">
    The home page of Fumadocs.
  </Card>
</Cards>

#### 无 href

```mdx
<Cards>
  <Card title="Fetching, Caching, and Revalidating">
    Learn more about `fetch` in Next.js.
  </Card>
</Cards>
```

<Cards>
  <Card title="Fetching, Caching, and Revalidating">
    Learn more about `fetch` in Next.js.
  </Card>
</Cards>

### 提示框

对于添加提示/警告很有用，默认包含。

```mdx
<Callout>Hello World</Callout>
```

<Callout>Hello World</Callout>

#### 标题

指定提示框标题。

```mdx
<Callout title="Title">Hello World</Callout>
```

<Callout title="Title">Hello World</Callout>

#### 类型

您可以指定提示框的类型。

- `info`（默认）
- `warn`
- `error`

```mdx
<Callout title="Title" type="error">
  Hello World
</Callout>
```

<Callout title="Title" type="error">
  Hello World
</Callout>

### 自定义组件

参见[所有 MDX 组件和可用选项](/docs/mdx)。

## 标题

每个标题会自动应用锚点，它会清理空格等无效字符。（例如，`Hello World` 变为 `hello-world`）

```md
# Hello `World`
```

### 目录设置

目录 (TOC) 将基于标题生成，您还可以自定义标题的效果：

```md
# Heading [!toc]

This heading will be hidden from TOC.

# Another Heading [toc]

This heading will **only** be visible in TOC, you can use it to add additional TOC items.
Like headings rendered in a React component:

<MyComp />
```

### 自定义锚点

您可以添加 `[#slug]` 来自定义标题锚点。

```md
# heading [#my-heading-id]
```

您也可以将其与目录设置链接起来，例如：

```md
# heading [toc] [#my-heading-id]
```

要将人们链接到特定标题，请将标题 ID 添加到哈希片段：`/page#my-heading-id`。

## 前言

我们支持 YAML 前言。这是一种指定文档常见信息（例如标题）的方式。
将其放在文档顶部。

```mdx
---
title: Hello World
---

## Title
```

有关前言可用属性的列表，请参见[页面约定](/docs/page-conventions#frontmatter)。

## 代码块

默认使用 [Rehype Code](/docs/headless/mdx/rehype-code) 支持语法高亮。

````mdx
```js
console.log('Hello World');
```
````

您可以为代码块添加标题。

````mdx
```js title="My Title"
console.log('Hello World');
```
````

### 高亮行

````md
```tsx
<div>Hello World</div>  // [\!code highlight]
<div>Hello World</div>
<div>Goodbye</div>
<div>Hello World</div>
```
````

### 高亮单词

您可以通过添加 `[!code word:<match>]` 来高亮特定单词。

````md
```js
// [\!code word:config]
const config = {
  reactStrictMode: true,
};
```
````

### 差异

````mdx
```ts
console.log('hewwo'); // [\!code --]
console.log('hello'); // [\!code ++]
```
````

```ts
console.log('hewwo'); // [!code --]
console.log('hello'); // [!code ++]
```

### 标签组

您可以使用 `<Tab />` 组件与代码块一起使用。

````mdx
import { Tab, Tabs } from 'fumadocs-ui/components/tabs';

<Tabs items={['Tab 1', 'Tab 2']}>
  <Tab value='Tab 1'>
    ```ts
    console.log('A');
    ```
  </Tab>
  <Tab value='Tab 2'>
    ```ts
    console.log('B');
    ```
  </Tab>
</Tabs>
````

> 注意，您可以在 MDX 文件中添加 MDX 组件，而不必导入它们。

<Tabs items={['Tab 1', 'Tab 2']}>
  <Tab value='Tab 1'>
    ```ts
    console.log('A');
    ```
  </Tab>
  <Tab value='Tab 2'>
    ```ts
    console.log('B');
    ```
  </Tab>
</Tabs>

### 使用 Typescript Twoslash

编写带有悬停类型信息和检测到类型错误的 Typescript 代码块。

默认情况下未启用。参见 [Twoslash](/docs/twoslash)。

## 图片

所有内置内容源都能正确处理图片。
图片会自动为 `next/image` 优化。

```mdx
![Image](/image.png)
```

![Image](/images/docs/notebook.png)

## 可选功能

一些您可以启用的可选插件。
