---
title: Files
description: Display file structure in your documentation
preview: 'files'
---

## Usage

Wrap file components in `Files`.

```mdx
import { File, Folder, Files } from 'fumadocs-ui/components/files';

<Files>
  <Folder name="app" defaultOpen>
    <File name="layout.tsx" />
    <File name="page.tsx" />
    <File name="global.css" />
  </Folder>
  <Folder name="components">
    <File name="button.tsx" />
    <File name="tabs.tsx" />
    <File name="dialog.tsx" />
  </Folder>
  <File name="package.json" />
</Files>
```

### File

{/* <AutoTypeTable path="./content/docs/props.ts" name="FileProps" /> */}

### Folder

{/* <AutoTypeTable path="./content/docs/props.ts" name="FolderProps" /> */}
