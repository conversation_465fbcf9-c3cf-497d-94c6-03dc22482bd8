---
title: 类型表格
description: 用于记录类型的表格
preview: typeTable
---

## 使用方法

它接受一个 `type` 属性。

```mdx
import { TypeTable } from 'fumadocs-ui/components/type-table';

<TypeTable
  type={{
    percentage: {
      description:
        '显示滚动按钮的滚动位置百分比',
      type: 'number',
      default: 0.2,
    },
  }}
/>
```

## 参考

### Type Table

{/* <AutoTypeTable path="./content/docs/props.ts" name="TypeTableProps" /> */}

### Object Type

{/* <AutoTypeTable path="./content/docs/props.ts" name="ObjectTypeProps" /> */} 