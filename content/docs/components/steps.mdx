---
title: Steps
description: Adding steps to your docs
preview: steps
---

## Usage

Put your steps into the `Steps` container.

```mdx
import { Step, Steps } from 'fumadocs-ui/components/steps';

<Steps>
<Step>

### Hello World

</Step>

<Step>

### Hello World

</Step>
</Steps>
```

> We recommend using Tailwind CSS utility classes directly on Tailwind CSS projects.

### Without imports

You can use the Tailwind CSS utilities without importing it.

```mdx
<div className="fd-steps">
  <div className="fd-step" />
</div>
```

It supports adding step styles to only headings with arbitrary variants.

```mdx
<div className='fd-steps [&_h3]:fd-step'>

### Hello World

</div>
```

<div className='fd-steps [&_h3]:fd-step'>

### Hello World

You no longer need to use the step component anymore.

</div>
