# 图片上传组件重构设计

## 概述

本设计文档描述了 AI 应用 (如 hairstyle 页面) 中两个图片上传组件的重构方案。重构的核心目标是提升用户体验和组件复用性，优化布局设计，并最大化利用 Ant Design 组件的功能。

### 目标

- 重构用户图片上传区域 (Upload Images)，支持可配置的最大图片数量
- 重构参考图片选择区域 (Pick a Prompt)，支持预置图片和用户自定义上传
- 实现统一的 3 列网格布局，响应式设计
- 复用 Ant Design Upload 组件，减少重复代码
- 提供一致的用户交互体验

## 技术架构

### 组件设计概览

```mermaid
graph TD
    A[hairstyle页面] --> B[用户图片上传区域]
    A --> C[参考图片选择区域]
    
    B --> D[MultiImageUpload组件]
    C --> E[ReferenceImageSelector组件]
    
    D --> F[Ant Design Upload]
    E --> F
    
    F --> G[图片验证]
    F --> H[云存储上传]
    F --> I[预览功能]
    
    subgraph "布局系统"
        J[3列网格布局]
        K[响应式设计]
        L[上传框固定位置]
    end
```

### 组件层次结构

```mermaid
graph LR
    A[页面容器] --> B[用户上传区域]
    A --> C[参考选择区域]
    
    B --> D[MultiImageUpload]
    D --> E[UploadButton - 固定第一位]
    D --> F[UploadedImage1]
    D --> G[UploadedImage2]
    D --> H[UploadedImageN...]
    
    C --> I[ReferenceImageSelector]
    I --> J[ReferenceUploadButton - 固定第一位]
    I --> K[PresetImage1 - 只读]
    I --> L[PresetImage2 - 只读]
    I --> M[UserUploadedImage - 可删除]
```

## 组件设计规范

### 1. MultiImageUpload 组件重构

#### 组件接口定义

```typescript
interface MultiImageUploadProps {
  onImagesChange: (files: File[]) => void;
  maxImages: number;
  disabled?: boolean;
  uploadedImages?: File[];
  className?: string;
}
```

#### 布局设计

| 位置 | 内容 | 行为 |
|------|------|------|
| [1,1] | 上传按钮 | 固定位置，始终显示 |
| [1,2] | 第 1 张图片 | 用户上传，可预览/删除 |
| [1,3] | 第 2 张图片 | 用户上传，可预览/删除 |
| [2,1] | 第 3 张图片 | 用户上传，可预览/删除 |
| [2,2] | 第 4 张图片 | 用户上传，可预览/删除 |
| [2,3] | 第 5 张图片 | 用户上传，可预览/删除 |
| ... | ... | 依此类推，每行 3 个 |

#### 功能特性

- **固定上传按钮**: 上传按钮始终位于第一个位置
- **动态网格扩展**: 新上传的图片按从左到右、从上到下的顺序排列
- **图片管理**: 支持预览、删除、重新排序
- **数量限制**: 根据 maxImages 参数限制上传数量
- **拖拽支持**: 支持拖拽上传和重新排序

### 2. ReferenceImageSelector 组件重构

#### 组件接口定义

```typescript
interface ReferenceImageSelectorProps {
  prompts: Prompt[];
  onPromptSelect: (prompt: Prompt) => void;
  onUploadedImageSelect: (file: File | null) => void;
  selectedPromptId?: string;
  uploadedReferenceImage?: File | null;
  disabled?: boolean;
  className?: string;
}
```

#### 布局设计

| 位置 | 内容 | 行为 |
|------|------|------|
| [1,1] | 参考图上传按钮 | 固定位置，用户自定义上传 |
| [1,2] | 预设图片 1 | 只读，可选择但不可删除 |
| [1,3] | 预设图片 2 | 只读，可选择但不可删除 |
| [2,1] | 预设图片 3 | 只读，可选择但不可删除 |
| [2,2] | 用户上传图片 | 如果用户上传了自定义参考图 |
| ... | ... | 依此类推 |

#### 功能特性

- **混合显示模式**: 预置图片和用户上传图片共存
- **选择状态管理**: 预置图片选择和用户上传互斥
- **权限差异化**: 预置图片只能预览，用户上传图片可删除
- **视觉区分**: 不同类型图片的视觉样式区分

## 交互流程设计

### 用户图片上传流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 上传界面
    participant C as MultiImageUpload组件
    participant V as 验证器
    participant S as 云存储
    participant P as 父组件

    U->>UI: 点击上传按钮或拖拽文件
    UI->>C: 触发文件选择事件
    C->>V: 验证文件类型和大小
    alt 验证失败
        V-->>UI: 显示错误提示
    else 验证成功
        V->>S: 上传文件到云存储
        S-->>C: 返回文件URL
        C->>UI: 更新界面显示
        C->>P: 通知父组件文件变更
    end
```

### 参考图片选择流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 选择界面
    participant C as ReferenceImageSelector组件
    participant P as 父组件

    alt 选择预置图片
        U->>UI: 点击预设图片
        UI->>C: 设置选中状态
        C->>P: 通知选择预置图片
        C->>C: 清除用户上传图片
    else 上传自定义图片
        U->>UI: 上传自定义图片
        UI->>C: 处理文件上传
        C->>P: 通知选择用户图片
        C->>C: 清除预置图片选择
    end
```

## 响应式设计

### 断点定义

| 断点 | 屏幕尺寸 | 列数 | 间距 |
|------|----------|------|------|
| Desktop | ≥768px | 3 列 | 0.75rem |
| Tablet | <768px | 2 列 | 0.375rem |
| Mobile | <480px | 1 列 | 0.25rem |

### 布局适配策略

```css
/* 基础网格布局 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.image-grid-item {
  width: calc(33.333% - 0.5rem);
  aspect-ratio: 1;
}

/* 平板适配 */
@media (max-width: 768px) {
  .image-grid-item {
    width: calc(50% - 0.375rem);
  }
}

/* 手机适配 */
@media (max-width: 480px) {
  .image-grid {
    gap: 0.25rem;
  }
  .image-grid-item {
    width: 100%;
  }
}
```

## 状态管理

### MultiImageUpload 状态管理

```typescript
interface MultiImageUploadState {
  fileList: UploadFile[];        // Ant Design 文件列表
  uploadedFiles: File[];         // 实际文件对象
  previewOpen: boolean;          // 预览弹窗状态
  previewImage: string;          // 预览图片 URL
  loading: Set<string>;          // 上传中的文件 ID
}
```

### ReferenceImageSelector 状态管理

```typescript
interface ReferenceImageSelectorState {
  selectedPromptId?: string;     // 选中的预置图片 ID
  userUploadedFile?: File;       // 用户上传的文件
  fileList: UploadFile[];        // 用户上传文件列表
  previewOpen: boolean;          // 预览弹窗状态
  previewImage: string;          // 预览图片 URL
  imageErrors: Set<string>;      // 图片加载错误集合
}
```

## 技术实现细节

### 文件验证规则

```typescript
const FILE_VALIDATION = {
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  maxSize: 10 * 1024 * 1024, // 10MB
  minDimensions: { width: 200, height: 200 },
  maxDimensions: { width: 4096, height: 4096 }
};
```

### 上传处理逻辑

```typescript
const handleFileUpload = async (file: File) => {
  // 1. 客户端验证
  if (!validateFile(file)) return;
  
  // 2. 显示上传进度
  updateFileStatus(file.uid, 'uploading');
  
  // 3. 上传到云存储
  try {
    const { url } = await uploadFileFromBrowser(file, 'ai-images');
    updateFileStatus(file.uid, 'done', url);
    notifyParentComponent();
  } catch (error) {
    updateFileStatus(file.uid, 'error');
    showErrorToast(error);
  }
};
```

### 组件复用策略

```typescript
// 共享 Upload 配置
const SHARED_UPLOAD_CONFIG = {
  listType: 'picture-card' as const,
  accept: 'image/*',
  showUploadList: {
    showPreviewIcon: true,
    showRemoveIcon: true,
    showDownloadIcon: false,
  },
  beforeUpload: validateFile,
  onPreview: handlePreview,
};

// 用户上传组件
<Upload {...SHARED_UPLOAD_CONFIG} multiple={true} />

// 参考图上传组件  
<Upload {...SHARED_UPLOAD_CONFIG} multiple={false} />
```

## 样式系统

### CSS 变量定义

```css
:root {
  --image-grid-gap: 0.75rem;
  --image-item-border-radius: 0.5rem;
  --upload-border-color: hsl(var(--border));
  --upload-hover-border-color: hsl(var(--primary) / 0.5);
  --upload-background: hsl(var(--muted) / 0.2);
  --upload-hover-background: hsl(var(--muted) / 0.3);
}
```

### 主题适配

```css
/* 选中状态 */
.image-item--selected {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
}

/* 上传中状态 */
.image-item--uploading {
  border-color: hsl(var(--primary));
  opacity: 0.8;
}

/* 错误状态 */
.image-item--error {
  border-color: hsl(var(--destructive));
  background-color: hsl(var(--destructive) / 0.1);
}
```

## 国际化支持

### 多语言文案

```json
{
  "uploadImage": {
    "title": "上传图片",
    "addImage": "添加图片", 
    "maxImages": "张图片",
    "maxFileSize": "最大文件大小：10MB",
    "supportedFormats": "支持 JPG、PNG、WebP 格式",
    "dragOrClick": "拖拽或点击上传",
    "uploading": "上传中...",
    "uploadSuccess": "上传成功",
    "uploadFailed": "上传失败，请重试"
  },
  "referenceImage": {
    "title": "选择参考图片",
    "uploadCustom": "上传自定义图片",
    "selectPreset": "选择预设图片",
    "previewImage": "预览图片",
    "removeImage": "删除图片"
  }
}
```

## 性能优化

### 图片优化策略

| 优化项 | 实现方案 |
|--------|----------|
| 懒加载 | 使用 Next.js Image 组件的 lazy loading |
| 压缩 | 客户端压缩超大图片 |
| 缓存 | 浏览器缓存已上传图片 |
| 预览生成 | 生成缩略图用于预览 |

### 内存管理

```typescript
// 清理对象 URL 防止内存泄漏
useEffect(() => {
  return () => {
    previewUrls.forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
      }
    });
  };
}, []);
```

## 验证方案

### 功能验证清单

- ✅ 拖拽上传文件功能正常
- ✅ 批量上传多个文件
- ✅ 网络中断时的错误处理
- ✅ 不同屏幕尺寸的响应式布局适配
- ✅ 主流浏览器兼容性验证
- ✅ 文件类型和大小验证
- ✅ 最大上传数量限制
- ✅ 预置图片和用户上传互斥逻辑
- ✅ 图片预览和删除功能
- ✅ 上传进度显示