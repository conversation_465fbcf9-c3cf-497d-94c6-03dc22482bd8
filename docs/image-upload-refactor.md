# Image Upload Component Refactor

## Overview

This refactor consolidates the image upload functionality across the figurine and hairstyle apps by creating a reusable `AntdImageUpload` component based on Ant Design's Upload component.

## Changes Made

### 1. Created New Reusable Component

**File**: `src/components/ai/antd-image-upload.tsx`

- Unified image upload component using Ant Design's Upload
- Supports both single and grid layouts
- Includes built-in preview modal
- Handles file validation (type, size)
- Contains all necessary CSS styles for proper display
- Provides consistent styling across different apps

### 2. Updated Figurine Page

**File**: `src/app/[locale]/(marketing)/apps/figurine/page.tsx`

**Changes**:
- Replaced custom Ant Design Upload implementation with `AntdImageUpload`
- Removed duplicate state management (`fileList`, `previewOpen`, `previewImage`)
- Removed custom CSS styles (now handled by the component)
- Simplified component usage to just a few props

**Before**:
```tsx
// Complex custom implementation with 60+ lines of Upload component code
<Upload
  accept="image/*"
  listType="picture-card"
  // ... many props and handlers
>
  {/* Custom upload UI */}
</Upload>
```

**After**:
```tsx
<AntdImageUpload
  onImagesChange={setUploadedImages}
  disabled={loading}
  maxImages={1}
  uploadedImages={uploadedImages}
  layout="single"
/>
```

### 3. Updated Hairstyle Page

**File**: `src/app/[locale]/(marketing)/apps/hairstyle/page.tsx`

**Changes**:
- Replaced `ImageUploadSection` with `AntdImageUpload`
- Now uses the same upload component as figurine page
- Consistent styling and behavior

**Before**:
```tsx
<ImageUploadSection
  onImagesChange={setUploadedImages}
  disabled={loading}
  maxImages={1}
  uploadedImages={uploadedImages}
/>
```

**After**:
```tsx
<AntdImageUpload
  onImagesChange={setUploadedImages}
  disabled={loading}
  maxImages={1}
  uploadedImages={uploadedImages}
  layout="single"
/>
```

## Component Features

### Props Interface

```tsx
interface AntdImageUploadProps {
  onImagesChange: (images: File[]) => void;
  disabled?: boolean;
  maxImages?: number;
  uploadedImages?: File[];
  layout?: 'grid' | 'single';
  className?: string;
  showTitle?: boolean;
  title?: string;
}
```

### Key Features

1. **File Validation**: Automatic validation for image types and file size (max 10MB)
2. **Preview Modal**: Built-in image preview functionality
3. **Responsive Layout**: Supports both single image and grid layouts
4. **Consistent Styling**: Unified appearance across all apps
5. **Error Handling**: Toast notifications for validation errors
6. **Accessibility**: Proper ARIA labels and keyboard navigation

### CSS Styling

The component includes comprehensive CSS styles that ensure:
- Proper aspect ratio maintenance (square containers)
- Full height/width utilization of containers
- Consistent border and background styling
- Proper image object-fit for uploaded images

## Benefits

1. **Code Reusability**: Single component used across multiple apps
2. **Consistency**: Uniform upload experience across the platform
3. **Maintainability**: Changes only need to be made in one place
4. **Reduced Bundle Size**: Eliminated duplicate code
5. **Better UX**: Consistent behavior and styling

## Migration Guide

To use the new component in other apps:

1. Import the component:
```tsx
import { AntdImageUpload } from '@/components/ai';
```

2. Replace existing upload implementations:
```tsx
<AntdImageUpload
  onImagesChange={handleImagesChange}
  disabled={isLoading}
  maxImages={1}
  uploadedImages={images}
  layout="single" // or "grid"
/>
```

3. Remove any custom upload-related state management and CSS styles

## Future Enhancements

- Support for drag-and-drop from external sources
- Progress indicators for large file uploads
- Image compression before upload
- Support for additional file formats
- Batch upload capabilities for grid layout
