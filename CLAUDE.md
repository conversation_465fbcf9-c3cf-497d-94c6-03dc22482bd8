# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

When updating this file, maintain consistency with the existing formatting style and structure used throughout the document.

## Development Commands

### Core Development
- `pnpm dev` - Start development server with content collections
- `pnpm build` - Build the application and content collections
- `pnpm start` - Start production server
- `pnpm lint` - Run Biome linter (use for code quality checks)
- `pnpm format` - Format code with Biome

### Database Operations (Drizzle ORM)
- `pnpm db:generate` - Generate new migration files based on schema changes
- `pnpm db:migrate` - Apply pending migrations to the database
- `pnpm db:push` - Sync schema changes directly to the database (development only)
- `pnpm db:studio` - Open Drizzle Studio for database inspection and management

### Content and Email
- `pnpm content` - Process MDX content collections
- `pnpm email` - Start email template development server on port 3333

## Project Architecture

This is a Next.js 15 full-stack SaaS application with the following key architectural components:

### Core Stack
- **Framework**: Next.js 15 with App Router
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth with social providers (Google, GitHub)
- **Payments**: Stripe integration with subscription and one-time payments
- **UI**: Radix UI components with TailwindCSS
- **State Management**: Zustand for client-side state
- **Internationalization**: next-intl with English and Chinese locales
- **Content**: Fumadocs for documentation and MDX for content
- **Code Quality**: Biome for formatting and linting

### Key Directory Structure
- `src/app/` - Next.js app router with internationalized routing
- `src/components/` - Reusable React components organized by feature
- `src/lib/` - Utility functions and shared code
- `src/db/` - Database schema and migrations
- `src/actions/` - Server actions for API operations
- `src/stores/` - Zustand state management
- `src/hooks/` - Custom React hooks
- `src/config/` - Application configuration files
- `src/i18n/` - Internationalization setup
- `src/mail/` - Email templates and mail functionality
- `src/payment/` - Stripe payment integration
- `src/credits/` - Credit system implementation
- `content/` - MDX content files for docs and blog
- `messages/` - Translation files (en.json, zh.json) for internationalization

### Naming Conventions
- **Component Files**: Use kebab-case for .tsx/.ts files (e.g., `user-profile.tsx`, `image-generator.tsx`)
- **Component Names**: Use PascalCase for actual component names (e.g., `UserProfile`, `ImageGenerator`)
- **Directory Names**: Use kebab-case (e.g., `user-management/`, `ai-image/`)
- **Utility Files**: Use kebab-case (e.g., `api-client.ts`, `string-utils.ts`)
- **Configuration Files**: Follow existing patterns in the codebase

### Authentication & User Management
- Uses Better Auth with PostgreSQL adapter
- Supports email/password and social login (Google, GitHub)
- Includes user management, email verification, and password reset
- Admin plugin for user management and banning
- Automatic newsletter subscription on user creation

### Payment System
- Stripe integration for subscriptions and one-time payments
- Three pricing tiers: Free, Pro (monthly/yearly), and Lifetime
- Credit system with packages for pay-per-use features
- Customer portal for subscription management

### Feature Modules
- **Blog**: MDX-based blog with pagination and categories
- **Docs**: Fumadocs-powered documentation
- **AI Features**: Image generation with multiple providers (OpenAI, Replicate, etc.)
- **Newsletter**: Email subscription system
- **Analytics**: Multiple analytics providers support
- **Storage**: S3 integration for file uploads

### Development Workflow
1. Use TypeScript for all new code
2. Follow Biome formatting rules (single quotes, trailing commas)
3. Write server actions in `src/actions/`
4. Use Zustand for client-side state management
5. Implement database changes through Drizzle migrations
6. Use Radix UI components for consistent UI
7. Follow the established directory structure
8. Use proper error handling with error.tsx and not-found.tsx
9. Leverage Next.js 15 features like Server Actions
10. Use `next-safe-action` for secure form submissions
11. Use next-intl for i18n - Never hardcode text in components. Use `useTranslations` hook and add translations to all language JSON files in the `messages/`
12. Always use `LocaleLink` from `@/i18n/navigation` instead of Next.js `Link` for internal navigation to preserve locale context. This prevents users from losing their language preference when navigating between pages
13. Use semantic color variables (`text-foreground`, `text-muted-foreground`) for automatic theme adaptation
14. Never use hardcoded colors - Always use theme variables (`primary`, `secondary`, `accent`, etc.) instead of specific color names (orange-500, blue-600, etc.) to maintain theme consistency and allow easy theme switching
15. Research existing patterns before development, test themes and responsiveness after completion, maintain strict UI detail control

### Boilerplate vs Custom Components

This codebase is based on a boilerplate that includes pre-built components. To better meet specific project requirements, custom components have been created alongside the boilerplate components.

Directory Structure:
- Boilerplate Components: `src/ai/` - Original boilerplate components (avoid modifying when possible)
- Custom Components: `src/components/ai/` - Project-specific custom components (preferred for modifications)

Development Priority:
When developing features, prioritize using and modifying custom components over boilerplate components. While boilerplate components can be modified if it's more convenient, this approach is preferred:
- Maintains boilerplate integrity for future updates
- Ensures customizations are preserved
- Provides better project-specific functionality

Component Origin Identification:
If uncertain whether a component is from the boilerplate or custom-built, check the git commit history:
- `javayhu` commits = Boilerplate components (avoid modifying when possible)
- `jeremy-feng` commits = Custom components (safe to modify)

Use `git log --follow -- path/to/file` or `git blame filename` to identify the component's origin.

Best Practice:
Before modifying any component, verify its origin using git history. If it's a boilerplate component, consider creating a custom alternative in `src/components/ai/` first. However, if modifying the boilerplate component directly is significantly more convenient, it's acceptable to do so.

### Configuration
- Main config in `src/config/website.tsx`
- Environment variables template in `env.example`
- Database config in `drizzle.config.ts`
- Biome config in `biome.json` with specific ignore patterns
- TypeScript config with path aliases (@/* for src/*)

### Testing and Quality
- Use Biome for linting and formatting
- TypeScript for type safety
- Environment variables for configuration
- Proper error boundaries and not-found pages
- Zod for runtime validation

## Important Notes

- The project uses pnpm as the package manager
- Database schema is in `src/db/schema.ts` with auth, payment, and credit tables
- Email templates are in `src/mail/templates/`
- The app supports both light and dark themes
- Content is managed through MDX files in the `content/` directory
- The project includes comprehensive internationalization support
