# Project Development Rules and Best Practices

本文档整理了项目中所有技术栈的最佳实践和开发规范，供 Trae AI 在代码生成和建议时参考。

## 项目结构规范

### 核心目录结构
- `src/app/`: Next.js app router 页面和布局
- `src/components/`: 可复用的 React 组件
- `src/lib/`: 工具函数和共享代码
- `src/db/`: 使用 Drizzle ORM 的数据库模式和迁移
- `src/stores/`: Zustand 状态管理
- `src/actions/`: 服务器操作和 API 路由
- `src/hooks/`: 自定义 React hooks
- `src/types/`: TypeScript 类型定义
- `src/i18n/`: 国际化设置
- `src/mail/`: 邮件模板和邮件功能
- `src/payment/`: 支付集成
- `src/analytics/`: 分析和跟踪
- `src/storage/`: 文件存储集成
- `src/notification/`: 发送通知

### Boilerplate 代码处理规则

**重要提醒：本项目基于现有的 boilerplate 构建，在开发过程中必须遵循以下规则：**

1. **不要修改 boilerplate 原有代码**：对于 boilerplate 中已存在的文件和代码，严禁进行修改或删除操作
2. **可以使用 boilerplate 代码**：允许引用、导入和使用 boilerplate 中已有的组件、函数、类型等
3. **新需求创建新文件**：当需要实现 boilerplate 中不存在的功能时，应该创建新的文件而不是修改现有文件
4. **查看 boilerplate 内容**：要了解哪些文件属于 boilerplate，可以使用以下命令切换到 boilerplate 分支：
   ```bash
   git switch update-mksaas
   ```
   在 `update-mksaas` 分支中的所有代码都是 boilerplate 的原始代码
5. **扩展而非修改**：如需扩展现有功能，应通过继承、组合或创建新的包装组件等方式实现

**违反此规则可能导致：**
- 与 boilerplate 更新冲突
- 代码维护困难
- 项目升级问题

请在开发过程中严格遵守这些规则，确保项目的可维护性和可升级性。

### 配置文件
- `next.config.ts`: Next.js 配置
- `drizzle.config.ts`: 数据库配置
- `biome.json`: 代码格式化和 linting 规则
- `tsconfig.json`: TypeScript 配置
- `components.json`: UI 组件配置

### 内容管理
- `content/`: MDX 内容文件
- `source.config.ts`: Fumadocs 源配置

## 开发工作流程

### 可用脚本
- `pnpm dev`: 启动开发服务器
- `pnpm build`: 构建应用程序和内容集合
- `pnpm start`: 启动生产服务器
- `pnpm lint`: 运行 Biome linter
- `pnpm format`: 使用 Biome 格式化代码
- `pnpm db:generate`: 基于模式更改生成新的迁移文件
- `pnpm db:migrate`: 应用待处理的迁移到数据库
- `pnpm db:push`: 直接同步模式更改到数据库（仅开发环境）
- `pnpm db:studio`: 打开 Drizzle Studio 进行数据库检查和管理
- `pnpm email`: 启动邮件模板开发服务器

### 开发流程
1. 所有新代码使用 TypeScript
2. 遵循 Biome 格式化规则
3. 在 `src/actions/` 中编写服务器操作
4. 使用 Zustand 进行客户端状态管理
5. 通过 Drizzle 迁移实现数据库更改
6. 使用 Radix UI 组件保持 UI 一致性
7. 遵循既定的目录结构
8. 为新功能编写测试
9. 添加新内容时更新内容集合
10. 使用 `env.example` 中的环境变量

### 代码风格
- 使用带 hooks 的函数组件
- 实现适当的错误处理
- 遵循 TypeScript 最佳实践
- 使用适当的类型定义
- 记录复杂逻辑
- 保持组件小而专注
- 使用适当的命名约定
- 使用绝对路径导入：所有导入语句必须使用 @ 别名而不是相对路径
- 代码文件中的代码和注释都要用地道规范的英文，而不能使用中文

## TypeScript 最佳实践

- 在 `tsconfig.json` 中启用严格模式以获得更好的类型检查
- 对对象形状使用接口，对联合或交集使用类型
- 尽可能利用类型推断以减少类型注释
- 对可复用组件和函数使用泛型
- 使用严格的 null 检查以防止 null 和 undefined 错误
- 对可复用组件使用泛型实现适当的类型推断
- 利用类型守卫和断言进行运行时类型检查
- 在终端中运行命令时默认使用 `pnpm` 作为包管理器

## React 最佳实践

- 使用带 hooks 的函数组件而不是类组件，以获得更好的性能和可读性
- 对复杂状态场景使用 Zustand 等库实现适当的状态管理
- 利用 React 19 的新功能，如 `use` hook，以获得更好的数据获取和 suspense 集成
- 使用 TypeScript 确保适当的 prop 类型验证以增强类型安全性
- 利用 `useCallback` 和 `useMemo` 进行性能优化
- 利用 `react-hook-form` 进行高效的表单处理和验证

## Next.js 最佳实践

- 利用 Next.js 15 的新功能，如服务器操作，以提高性能和安全性
- 使用 `error.tsx` 和 `not-found.tsx` 实现适当的错误处理，以获得更好的用户体验
- 使用 `next-safe-action` 进行安全的表单提交和 API 调用
- 对客户端组件使用 `use client` 指令以优化服务器端渲染
- 利用 `next-themes` 进行简单的主题管理和深色模式支持

## 数据库和状态管理

### 数据库 (Drizzle ORM)
- 在 `src/db/schema.ts` 中定义模式
- 迁移文件在 `src/db/migrations`
- 使用 `db:generate` 基于模式更改创建新的迁移文件
- 使用 `db:migrate` 应用待处理的迁移到数据库
- 使用 `db:push` 直接同步模式更改到数据库（仅开发环境）
- 使用 `db:studio` 通过 Drizzle Studio UI 查看和管理数据库数据
- 遵循表和列的命名约定
- 使用适当的数据类型和约束
- 实现适当的索引
- 正确处理关系
- 在需要时使用事务
- 使用 Drizzle 的类型安全查询构建器以获得更好的代码完成和安全性
- 使用 Drizzle Kit 实现迁移以进行数据库模式管理
- 利用 Drizzle 的关系系统定义和查询关系
- 使用 Drizzle 的事务 API 进行复杂的原子操作

### 状态管理 (Zustand)
- 在 `src/stores/` 中定义存储
- 保持存储模块化和专注
- 对存储类型使用 TypeScript
- 实现适当的状态更新
- 正确处理异步操作
- 对派生状态使用选择器
- 实现适当的错误处理
- 在需要时使用中间件
- 保持存储逻辑纯净
- 记录复杂的状态逻辑
- 使用 `create` 函数定义存储以获得简单性和性能
- 实现 `persist` 等中间件以在会话间持久化状态
- 利用 `useStore` hook 在组件中访问存储状态
- 利用 `immer` 中间件通过可变语法更容易地更新状态

### 数据流
1. 服务器组件中的服务器端数据获取
2. Zustand 存储中的客户端状态
3. React Hook Form 中的表单状态
4. 通过服务器操作进行 API 调用
5. 通过 Drizzle 进行数据库操作
6. 通过 AWS S3 进行文件存储
7. 每层适当的错误处理
8. 全程类型安全
9. 使用 Zod 进行适当验证
10. 适当的缓存策略

## UI 和组件指南

### 组件结构
- 组件在 `src/components/`
- 遵循原子设计原则
- 使用 Radix UI 原语
- 实现适当的可访问性
- 使用 Tailwind CSS 进行样式设计
- 遵循一致的命名
- 保持组件专注
- 实现适当的错误状态
- 处理加载状态
- 使用适当的 TypeScript 类型

### UI 库
- Radix UI 用于原语
- Tailwind CSS 用于样式设计
- Framer Motion 用于动画
- React Hook Form 用于表单
- Zod 用于验证
- Lucide React 用于图标
- Tabler Icons 用于额外图标
- Sonner 用于提示
- Vaul 用于抽屉
- Embla Carousel 用于轮播

### 样式指南
- 使用 Tailwind CSS 类
- 遵循设计系统令牌
- 实现深色模式支持
- 使用适当的间距比例
- 遵循调色板
- 实现响应式设计
- 使用适当的排版
- 处理悬停/焦点状态
- 实现适当的过渡
- 使用适当的 z-index 比例

### 可访问性
- 使用语义 HTML
- 实现适当的 ARIA 标签
- 处理键盘导航
- 支持屏幕阅读器
- 使用适当的颜色对比度
- 实现焦点管理
- 处理动态内容
- 支持减少动画
- 使用辅助工具测试
- 遵循 WCAG 指南

## Tailwind CSS 最佳实践

- 使用实用优先的方法进行快速开发和可维护性
- 使用 Tailwind 的内置断点实现响应式设计
- 利用 `@apply` 指令创建自定义实用类
- 利用 Tailwind 的 JIT 模式提高性能和减小包大小
- 利用 `tailwind-merge` 进行高效的类合并和覆盖
- 利用 `tailwindcss-animate` 进行简单的动画实现

## Radix UI 最佳实践

- 使用 Radix UI 原语构建自定义、可访问的组件
- 利用 Radix UI 的组合模式创建复杂、可复用的 UI 元素
- 实现适当的 ARIA 属性和键盘导航以获得可访问性
- 利用 Radix UI 的内置状态管理处理复杂组件
- 使用 `asChild` prop 自定义 Radix UI 组件以获得更好的灵活性

## 表单处理 (React Hook Form)

- 使用 `useForm` hook 进行高效的表单状态管理
- 使用 Zod 和 `@hookform/resolvers` 实现验证以获得类型安全的表单验证
- 利用 `Controller` 组件与自定义输入集成
- 利用 `useFormContext` hook 在组件间共享表单状态

## 模式验证 (Zod)

- 定义清晰且可复用的数据验证模式
- 使用 Zod 的类型推断进行 TypeScript 集成和类型安全
- 使用 Zod 的 `refine` 方法实现自定义验证规则
- 将 Zod 与 `react-hook-form` 结合使用以实现无缝表单验证

## 日期和时间处理 (date-fns)

- 使用 `format` 函数在整个应用程序中保持一致的日期格式
- 使用 `utcToZonedTime` 函数实现适当的时区处理
- 利用 `intervalToDuration` 函数计算时间差
- 利用 `isWithinInterval` 函数进行日期范围检查

## AI SDK 最佳实践

- 使用 `createClient` 函数初始化 OpenAI 客户端
- 对 API 请求实现适当的错误处理和速率限制
- 在长时间运行的任务中利用流式响应以获得更好的用户体验
- 利用 `ai` 包与 React 组件轻松集成
- 遵循 OpenAI 的负责任 AI 使用和数据隐私指南

## 支付集成 (Stripe)

- 使用 `@stripe/stripe-js` 包进行客户端 Stripe 集成
- 对支付流程实现适当的错误处理和用户反馈
- 利用 Stripe 的 Elements 进行安全、可定制的支付输入
- 利用 Stripe 的 Webhooks 进行实时支付状态更新
- 遵循 Stripe 的 PCI 合规性和安全最佳实践

## 总结

这些规则和最佳实践涵盖了项目中使用的所有主要技术栈。在开发过程中，请始终遵循这些指南以确保代码质量、可维护性和一致性。对于任何新的技术或库的引入，请确保更新相应的最佳实践文档。